﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PigGameLibrary.Databases;
using PigGameLibrary.Models;

namespace PigGameLibrary.Data
{
    public class UserData : IUserData
    {
        private readonly ISqlDataAccess _db;
        private const string connectionStringName = "SqlDb";
        public UserData(ISqlDataAccess db)
        {
            _db = db;
        }
        public Task<List<UserModel>> GetAllUsers()
        {
            return _db.LoadData<UserModel, dynamic>(
                "dbo.spGetAllUsers", new { }, connectionStringName, true);
        }
        public async Task<UserModel?> GetUserById(int id)
        {
            var results = await _db.LoadData<UserModel, dynamic>(
                "dbo.spUserById",
                    new { userId = id },
                    connectionStringName,
                    true);

            return results.FirstOrDefault();

        }

        public async Task<UserModel?> GetUserByName(string username)
        {
            var results = await _db.LoadData<UserModel, dynamic>(
                "dbo.spUserByName",
                new { UserName = username },
                connectionStringName,
                true);
            return results.FirstOrDefault();
        }

        //public async Task DeleteUserId(int id)
        //{
        //    await _db.SaveData("dbo.spDeleteUserById",
        //        new { userId = id },
        //        connectionStringName,
        //        true);
        //}

        public async Task<UserModel?> CreateUser(string username, string displayname, string email, string role = "User")
        {
            var results = await _db.LoadData<UserModel, dynamic>("dbo.spCreateNewUser",
                new { UserName = username, DisplayName = displayname, Email = email, Role = role },
                connectionStringName,
                true);

            return results.FirstOrDefault();

        }

        public async Task<int> DeleteUserById(int id)
        {
            return await _db.ExecuteWithRowCount(
                "dbo.spDeleteUserById",
                new { UserId = id },
                connectionStringName,
                true);
        }

        public async Task<int> EditUserCoins(int userId, int coins)
        {
            return await _db.ExecuteWithRowCount(
                "dbo.spEditCoinsUser",
                new { UserId = userId, Coins = coins },
                connectionStringName,
                true);
        }

        public async Task<List<LeaderboardModel>> GetLeaderboardData()
        {
            var results = await _db.LoadData<LeaderboardModel, dynamic>(
                "dbo.spGetLeaderboard",
                new { },
                connectionStringName,
                true);

            return results;
        }

        public async Task FeedPigCoins(int userId, int coinsToFeed)
        {
            await _db.SaveData(
                "dbo.spFeedPigCoins",
                new { UserId = userId, CoinsToFeed = coinsToFeed },
                connectionStringName,
                true);
        }

        public async Task<UserModel?> UpdateUserLastLogin(string username)
        {
            var results = await _db.LoadData<UserModel, dynamic>(
                "dbo.spUpdateUserLastLogin",
                new { UserName = username },
                connectionStringName,
                true);

            return results.FirstOrDefault();
        }

        public async Task<int> UpdateUserRole(int userId, string role)
        {
            return await _db.ExecuteWithRowCount(
                "dbo.spUpdateUserRole",
                new { UserId = userId, Role = role },
                connectionStringName,
                true);
        }
    }
}
