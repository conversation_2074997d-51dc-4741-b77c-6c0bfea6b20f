// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router';
import DashboardPage from '@/pages/DashboardPage.vue';
import PigDesigner from '@/pages/PigDesigner.vue';
import TasksAndGoals from '@/pages/TasksAndGoals.vue';
import LeaderboardPage from '@/pages/LeaderboardPage.vue';
import FeedingArea from '@/pages/FeedingArea.vue';
import RewardsPage from '@/pages/RewardsPage.vue';

// Lazy-loaded admin pages
const AdminDashboard = () => import('@/pages/admin/AdminDashboard.vue');
const UserTasksPage = () => import('@/pages/admin/UserTasksPage.vue');
const UsersPage = () => import('@/pages/admin/UsersPage.vue');
import LogsPage from '@/pages/admin/LogsPage.vue';

// Store imports for navigation guards
import { useAuthStore } from '@/stores/authStore';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'DashboardPage',
      component: DashboardPage,
    },
    {
      path: '/design',
      name: 'PigDesigner',
      component: PigDesigner,
    },
    {
      path: '/tasks',
      name: 'TasksAndGoals',
      component: TasksAndGoals,
    },
    {
      path: '/leaderboard',
      name: 'LeaderboardPage',
      component: LeaderboardPage,
    },
    {
      path: '/feed',
      name: 'FeedingArea',
      component: FeedingArea,
    },
    {
      path: '/rewards',
      name: 'RewardsPage',
      component: RewardsPage,
    },
    // Admin routes
    {
      path: '/admin',
      component: () => import('@/components/admin/AdminLayout.vue'),
      meta: { requiresAdmin: true },
      children: [
        {
          path: '',
          name: 'AdminDashboard',
          component: AdminDashboard,
          meta: { requiresAdmin: true },
        },
        {
          path: 'user-tasks',
          name: 'AdminUserTasks',
          component: UserTasksPage,
          meta: { requiresAdmin: true },
        },
        {
          path: 'users',
          name: 'AdminUsers',
          component: UsersPage,
          meta: { requiresAdmin: true, requiresSuperAdmin: true },
        },
        {
          path: 'logs',
          name: 'AdminLogs',
          component: LogsPage,
          meta: { requiresAdmin: true, requiresSuperAdmin: true },
        },
      ],
    },
  ],
});

// Navigation guards
router.beforeEach((to, _from, next) => {
  // Check if the route requires admin access
  if (to.matched.some((record) => record.meta.requiresAdmin)) {
    const authStore = useAuthStore();

    if (!authStore.canAccessAdmin()) {
      // Redirect to home if not an admin
      next({ name: 'DashboardPage' });
      return;
    }

    // Check if the route requires super admin access
    if (
      to.matched.some((record) => record.meta.requiresSuperAdmin) &&
      !authStore.isSuperAdmin
    ) {
      // Redirect to admin dashboard if not a super admin
      next({ name: 'AdminUserTasks' });
      return;
    }
  }

  next();
});

export default router;
