﻿CREATE PROCEDURE [dbo].[spCreateNewUserTask]
    @TaskId int,
    @UserId int,
    @CoinReward int,
    @LoanAppId int
AS
BEGIN
    SET NOCOUNT ON;

    -- Insert new user task
    INSERT INTO UserTasks(TaskId, UserId, CoinReward, LoanAppId, CompletedDate)
    VALUES (
        @TaskId,
        @UserId,
        @CoinReward,
        @LoanAppId,
        GETDATE()
    );
    
    DECLARE @NewUserTaskId int = SCOPE_IDENTITY();

    -- Update user's UnclaimedCoins
    IF EXISTS (SELECT 1 FROM Users WHERE Id = @UserId)
    BEGIN
        UPDATE Users
        SET UnclaimedCoins = ISNULL(UnclaimedCoins, 0) + @CoinReward
        WHERE Id = @UserId;
        
        -- Return the newly created user task
        SELECT * FROM UserTasks WHERE Id = @NewUserTaskId;
    END
    ELSE
    BEGIN
        RAISERROR('User not found', 16, 1);
    <PERSON><PERSON>
