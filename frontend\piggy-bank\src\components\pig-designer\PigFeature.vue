<!-- src/components/pig-designer/parts/PigFeature.vue -->
<script setup lang="ts">
import { computed } from 'vue';
import { useSvgLoader } from '@/composables/useSvgLoader';

const props = defineProps<{
  path: string;
  x?: number;
  y?: number;
  scale?: number;
}>();

const { svgContent } = useSvgLoader(props.path);

const transformString = computed(() => {
  const translate = `translate(${props.x || 0}, ${props.y || 0})`;
  const scale = props.scale ? ` scale(${props.scale})` : '';
  return `${translate}${scale}`;
});
</script>

<template>
  <g class="pig-part" :transform="transformString">
    <!-- eslint-disable vue/no-v-text-v-html-on-component -->
    <g v-html="svgContent"></g>
    <!-- eslint-enable vue/no-v-text-v-html-on-component -->
  </g>
</template>
