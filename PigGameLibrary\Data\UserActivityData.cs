using Microsoft.Extensions.Configuration;
using PigGameLibrary.Databases;
using PigGameLibrary.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PigGameLibrary.Data
{
    public class UserActivityData : IUserActivityData
    {
        private readonly ISqlDataAccess _db;
        private readonly string connectionStringName = "SqlDb";

        public UserActivityData(ISqlDataAccess db)
        {
            _db = db;
        }

        public async Task<int> CreateUserActivity(int userId, string type, string message, string? metadataJson = null)
        {
            var parameters = new
            {
                UserId = userId,
                Type = type,
                Message = message,
                MetadataJson = metadataJson
            };

            var result = await _db.LoadData<int, dynamic>(
                "dbo.spCreateUserActivity",
                parameters,
                connectionStringName,
                true);

            return result.Count > 0 ? result[0] : -1;
        }

        public async Task<List<UserActivityModel>> GetUserActivitiesByUserId(int userId, int limit = 10)
        {
            var results = await _db.LoadData<UserActivityModel, dynamic>(
                "dbo.spGetUserActivitiesByUserId",
                new { UserId = userId, Limit = limit },
                connectionStringName,
                true);

            return results;
        }
    }
}