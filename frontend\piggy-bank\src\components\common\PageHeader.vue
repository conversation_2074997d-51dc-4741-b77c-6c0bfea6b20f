<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  subTitle: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="mb-6">
    <h1 class="flex items-center text-3xl font-bold text-pink-100">
      <span class="mr-2">🐷</span> {{ props.title }}
    </h1>
    <p v-if="props.subTitle" class="text-pink-100">{{ props.subTitle }}</p>
  </div>
</template>
