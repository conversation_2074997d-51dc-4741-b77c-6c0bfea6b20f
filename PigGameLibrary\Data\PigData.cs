﻿using PigGameLibrary.Databases;
using PigGameLibrary.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading.Tasks.Dataflow;

namespace PigGameLibrary.Data
{
    public class PigData : IPigData
    {
        private readonly ISqlDataAccess _db;
        private const string connectionStringName = "SqlDb";
        public PigData(ISqlDataAccess db)
        {
            _db = db;
        }

        public async Task<PigModel?> GetPigById(int id)
        {
            var results = await _db.LoadData<PigModel, dynamic>(
                "dbo.spPigById",
                new { id = id },
                connectionStringName,
                true);
            return results.FirstOrDefault();
        }

        public async Task<PigModel?> GetPigByUserId(int id)
        {
            var results = await _db.LoadData<PigModel, dynamic>(
                "dbo.spPigByUserId",
                new { userId = id },
                connectionStringName,
                true);

            return results.FirstOrDefault();

        }

        public async Task<PigModel> Create<PERSON>ig(string name, int userId, string facePropId, string eyeId, string eyebrowId, string mouthId)
        {
            var results = await _db.LoadData<PigModel, dynamic>("dbo.spCreatePig",
                new
                {
                    Name = name,
                    UserId = userId,
                    FacePropId = facePropId,
                    EyeId = eyeId,
                    EyebrowId = eyebrowId,
                    MouthId = mouthId
                },
                connectionStringName,
                true);
            return results.FirstOrDefault();
        }

        public async Task<bool> UpdatePig(int id, string name, string? facePropId,
                                        string? eyeId, string? eyebrowId, string? mouthId)
        {
            try
            {
                await _db.SaveData(
                    "dbo.spUpdatePig",
                    new
                    {
                        Id = id,
                        Name = name,
                        FacePropId = facePropId,
                        EyeId = eyeId,
                        EyebrowId = eyebrowId,
                        MouthId = mouthId,
                        LastModifiedDate = DateTime.UtcNow
                    },
                    connectionStringName,
                    true);

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
