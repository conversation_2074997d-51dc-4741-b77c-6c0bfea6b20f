<script setup lang="ts">
import { onMounted, ref } from 'vue';
import PageHeader from '@/components/common/PageHeader.vue';
import BasePanel from '@/components/common/BasePanel.vue';
import UserPig from '@/components/pig-designer/UserPig.vue';
import LoadingSpinner from '@/components/common/LoadingSpinner.vue';
import LeaderboardProgress from '@/components/common/LeaderboardProgress.vue';
import CoinReward from '@/components/common/CoinReward.vue';

import { useUserStore } from '@/stores/userStore';

const userStore = useUserStore();
const isFeeding = ref(false);
const errorMessage = ref('');
const showError = ref(false);
const showCoinAnimation = ref(false);
const showPigAnimation = ref(false);
const coinsToAnimate = ref(1);
const currentSpeechBubbleMessage = ref('');

const speechBubbleMessages = [
  'Oink! Delicious! 🐷',
  'Yummy coins! ⭐',
  'Feed me more! 🌟',
  "I'm growing stronger! 💪",
  'Nom nom nom! 🎉',
  'So tasty! 🏆',
  'More please! 💫',
  "I'm getting bigger! 🔥",
  'Oink oink! ⭐',
  'Thank you! 🌟',
];

const getRandomMessage = () => {
  const randomIndex = Math.floor(Math.random() * speechBubbleMessages.length);
  return speechBubbleMessages[randomIndex];
};

onMounted(async () => {
  await userStore.loadUserInfo();
  await userStore.loadLeaderboard();
});

async function feedCoin() {
  if (isFeeding.value) return;

  isFeeding.value = true;
  showError.value = false;
  coinsToAnimate.value = 1;

  // Start coin animation
  showCoinAnimation.value = true;

  // Wait for coin animation to complete before updating backend
  setTimeout(async () => {
    try {
      const success = await userStore.feedPig(1);
      if (success) {
        // Show pig animation after successful feeding
        currentSpeechBubbleMessage.value = getRandomMessage();
        showPigAnimation.value = true;

        // Hide pig animation after 3 seconds
        setTimeout(() => {
          showPigAnimation.value = false;
        }, 3000);

        // Refresh leaderboard data to update ranks
        await userStore.loadLeaderboard();
      }
    } catch (error) {
      errorMessage.value = 'Failed to feed coin. Please contact IT support.';
      showError.value = true;
      console.error(error);
    } finally {
      isFeeding.value = false;
      // Reset coin animation after a delay
      setTimeout(() => {
        showCoinAnimation.value = false;
      }, 500);
    }
  }, 1500); // Wait for coin animation to complete
}

async function feedAllCoins() {
  if (isFeeding.value || userStore.unclaimedCoins <= 0) return;

  isFeeding.value = true;
  showError.value = false;
  coinsToAnimate.value = Math.min(5, userStore.unclaimedCoins); // Limit to 5 coins for animation

  // Start coin animation
  showCoinAnimation.value = true;

  // Wait for coin animation to complete before updating backend
  setTimeout(async () => {
    try {
      const success = await userStore.feedPig(userStore.unclaimedCoins);
      if (success) {
        // Show pig animation after successful feeding
        currentSpeechBubbleMessage.value = getRandomMessage();
        showPigAnimation.value = true;

        // Hide pig animation after 3 seconds
        setTimeout(() => {
          showPigAnimation.value = false;
        }, 3000);

        // Refresh leaderboard data to update ranks
        await userStore.loadLeaderboard();
      }
    } catch (error) {
      errorMessage.value = 'Failed to feed coins. Please contact IT support.';
      showError.value = true;
      console.error(error);
    } finally {
      isFeeding.value = false;
      // Reset coin animation after a delay
      setTimeout(() => {
        showCoinAnimation.value = false;
      }, 500);
    }
  }, 1500); // Wait for coin animation to complete
}
</script>

<template>
  <div class="p-6">
    <div class="max-w-8xl mx-auto">
      <PageHeader
        title="Feeding Area"
        subTitle="Feed your pig and watch it grow!"
      />

      <div class="grid grid-cols-1 gap-6 lg:grid-cols-12">
        <!-- Pig display area -->
        <div class="md:col-span-8">
          <div
            class="bg-barn-background relative flex min-h-[500px] items-center justify-center rounded-xl border border-pink-100 bg-cover bg-center"
          >
            <div class="relative">
              <!-- Animated coins -->
              <div v-if="showCoinAnimation" class="coin-animation-container">
                <div
                  v-for="i in coinsToAnimate"
                  :key="i"
                  class="animated-coin"
                  :style="{
                    animationDelay: `${(i - 1) * 0.2}s`,
                    left: `${Math.random() * 40 - 20}px`,
                  }"
                >
                  <CoinReward :amount="1" />
                </div>
              </div>

              <div class="trough">
                <div class="trough-inner"></div>
              </div>
              <!-- Pig with speech bubble -->
              <div :class="{ 'pig-bounce': showPigAnimation }">
                <UserPig size="medium" />

                <Transition
                  enter-active-class="transition-opacity duration-300"
                  leave-active-class="transition-opacity duration-300"
                  enter-from-class="opacity-0"
                  leave-to-class="opacity-0"
                >
                  <div v-if="showPigAnimation" class="speech-bubble">
                    {{ currentSpeechBubbleMessage }}
                  </div>
                </Transition>
              </div>
            </div>
          </div>
        </div>

        <!-- Feeding controls -->
        <div class="md:col-span-4">
          <BasePanel title="Feed Your Pig" class="min-h-full w-96">
            <!-- Coin counter -->
            <div
              class="relative mb-6 flex items-center justify-between rounded-lg bg-yellow-100 p-4"
              id="coin-source"
            >
              <span class="text-2xl font-bold"
                >💰 {{ userStore.unclaimedCoins }}</span
              >
              <span class="text-sm text-gray-700">Unclaimed Coins</span>
            </div>

            <!-- Feed buttons -->
            <div class="mb-6 grid grid-cols-2 gap-3">
              <button
                @click="feedCoin"
                :disabled="isFeeding || userStore.unclaimedCoins <= 0"
                class="flex items-center justify-center rounded-lg bg-yellow-500 px-4 py-3 font-bold text-yellow-900 transition-colors hover:bg-yellow-400 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <span v-if="isFeeding" class="mr-2"><LoadingSpinner /></span>
                <span v-else class="mr-2">🪙</span>
                <span>{{ isFeeding ? 'Feeding...' : 'Feed Coin' }}</span>
              </button>

              <button
                @click="feedAllCoins"
                :disabled="isFeeding || userStore.unclaimedCoins <= 0"
                class="flex items-center justify-center rounded-lg bg-green-500 px-4 py-3 font-bold text-green-900 transition-colors hover:bg-green-400 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <span v-if="isFeeding" class="mr-2"><LoadingSpinner /></span>
                <span v-else class="mr-2">🪙🪙🪙</span>
                <span>{{ isFeeding ? 'Feeding...' : 'Feed All' }}</span>
              </button>
            </div>

            <transition name="fade">
              <div
                v-if="showError"
                class="mb-4 rounded-lg bg-red-100 p-2 text-center text-red-700"
              >
                {{ errorMessage }}
              </div>
            </transition>

            <LeaderboardProgress />
          </BasePanel>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-barn-background {
  background-image: url('/assets/backgrounds/5-farm-life-bg.png');
  background-size: 120%;
  background-position: center bottom;
}
.trough {
  width: 150px;
  height: 40px;
  background-color: #d2b48c;
  position: absolute;
  bottom: 5px;
  left: -10%;
  transform: translateX(-50%);
  border-radius: 10px 10px 0 0;
  border: 3px solid #8b4513;
  box-shadow: inset 0 4px 6px rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.trough::before,
.trough::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 40px;
  background-color: #8b4513;
  bottom: -40px;
  border-radius: 2px;
}

.trough::before {
  left: 15px;
}

.trough::after {
  right: 15px;
}

.trough-inner {
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 0;
  background-color: #f5deb3;
  border-radius: 8px 8px 0 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* Coin animation */
.coin-animation-container {
  position: absolute;
  bottom: 20px; /* Position lower to be in front of the pig */
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 2;
}

.animated-coin {
  position: absolute;
  animation: floatCoin 1.5s ease-in-out forwards;
  transform-origin: center;
  bottom: 0;
}

@keyframes floatCoin {
  0% {
    transform: translate(300px, -100px) rotate(0deg);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  100% {
    transform: translate(-50px, 20px) rotate(360deg); /* Position coins to land in trough */
    opacity: 0;
  }
}

/* Pig animation */
.pig-bounce {
  animation: quickBounce 0.8s ease-in-out infinite;
}

@keyframes quickBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-20px);
  }
  80% {
    transform: translateY(0);
  }
}

/* Speech bubble */
.speech-bubble {
  position: absolute;
  background: white;
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  top: -60px;
  left: 50%;
  transform: translateX(-50%);
  font-weight: bold;
  border: 2px solid #5d4037;
  margin-bottom: 10px;
  min-width: max-content;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.speech-bubble::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: white transparent transparent;
}
</style>
