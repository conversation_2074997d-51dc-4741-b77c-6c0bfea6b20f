# Piggy Bank Project

A full-stack application featuring a virtual pig that users can customize, feed, and earn coins for. The application consists of a Vue.js frontend and a .NET API backend with SQL Server database.

## Requirements

- Node.js (v18+)
- .NET 9 SDK
- SQL Server (Developer/Express version for local development or full instance for server deploy)
- Visual Studio (for database deployment and backend development)
- Visual Studio Code (recommended for front-end development)

## Getting Started

### Database Setup

1. Open the solution in Visual Studio
2. Double click database/PigGame.Database/Publish Profiles/PigGame.Database.publish.xml
3. Click "Publish"

This will create the database on your localdb and populate it with sample data.

### Backend Setup

1. Add the following to your user secrets:

```json
{
  "ConnectionStrings": {
    "SqlDb": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=PigGame.Database;Integrated Security=True;Connect Timeout=30;Encrypt=False;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False"
  }
}
```

### Frontend Setup

1. Navigate to the frontend directory:

```bash
cd frontend/piggy-bank
```

2. Install dependencies:

```bash
npm install
```

## Running the Application

### Using VS Code

The project includes launch configurations for VS Code:

1. Open the project in VS Code
2. Go to the "Run and Debug" tab
3. Select "Full Stack" from the dropdown
4. Click the play button or press F5

This will launch both the backend API and frontend development server.

Alternatively, you can run them individually:

- Select "Backend API" to run only the .NET API
- Select "Frontend (npm run dev)" to run only the Vue.js frontend

### Using Visual Studio and Command Line

1. Open the solution in Visual Studio and run the `PigGame.API` project
2. In a terminal, navigate to `frontend/piggy-bank` and run `npm run dev`

## Recommended VS Code Extensions

- Vue.volar - Vue language support
- ESLint - JavaScript/TypeScript linting
- Prettier - Code formatting
- Pretty TypeScript Errors - Improved TypeScript error messages
- C# Dev Kit - C# language support
- Tailwind CSS IntelliSense - Tailwind CSS support

## Project Structure

### Frontend Architecture

The frontend is built with Vue 3, using the Composition API and TypeScript. It uses Pinia for state management and Vue Router for navigation.

#### Key Directories

- `src/components` - Reusable Vue components
- `src/pages` - Page components that correspond to routes
- `src/stores` - Pinia stores for state management
- `src/services` - API service functions
- `src/router` - Route definitions

#### Routing

Routes are defined in `src/router/index.ts`:

```typescript
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: "/",
      name: "DashboardPage",
      component: DashboardPage,
    },
    {
      path: "/design",
      name: "PigDesigner",
      component: PigDesigner,
    },
    // Other routes...
  ],
});
```

To navigate programmatically:

```typescript
import { useRouter } from "vue-router";

const router = useRouter();
router.push("/design");
```

#### State Management

The application uses Pinia stores for state management:

```typescript
import { defineStore } from "pinia";
import { ref, computed } from "vue";

export const useUserStore = defineStore("user", () => {
  // State
  const _balance = ref({
    coins: 0,
  });
  const userInfo = ref<UserInfo | null>(null);

  // Computed
  const coins = computed(() => _balance.value.coins);

  // Actions
  async function loadUserInfo() {
    // Implementation...
  }

  return { userInfo, coins, loadUserInfo };
});
```

Using a store in a component:

```typescript
import { useUserStore } from "@/stores/userStore";

const userStore = useUserStore();
console.log(userStore.coins);
await userStore.loadUserInfo();
```

### Backend Architecture

The backend is a .NET API using the repository pattern for data access.

#### Key Components

- Controllers - Handle HTTP requests and responses
- Data Access - Classes that interact with the database
- Models - Data transfer objects

#### API Endpoints

The API includes endpoints for:

- User management
- Pig customization
- Tasks and achievements
- Leaderboard

Example controller method:

```csharp
[HttpPost]
public async Task<ActionResult<PigModel>> Create([FromBody] CreatePigRequest request)
{
    // Verify user exists
    var user = await _userDb.GetUserById(request.UserId);
    if (user == null)
        return BadRequest("User does not exist.");

    // Implementation...
}
```

## Authentication

The application uses Windows Authentication. The backend is configured to use Negotiate authentication, and the frontend includes credentials in API requests.

## Development Workflow

1. Start both the backend and frontend using the "Full Stack" launch configuration
2. Make changes to the code
3. The frontend has hot module replacement, so changes will be reflected immediately
4. For backend changes, you'll need to restart the API

## Troubleshooting

- For database connection issues, verify your connection string
- Authentication might fail when first starting, just refresh the page. The frontend starts too quick for the backend
