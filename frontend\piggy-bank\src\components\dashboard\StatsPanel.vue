<!-- src/components/dashboard/StatsPanel.vue -->
<script setup lang="ts">
import LeaderboardProgress from '@/components/common/LeaderboardProgress.vue';
import UserBalance from '../common/UserBalance.vue';
import BasePanel from '../common/BasePanel.vue';
</script>
<template>
  <BasePanel title="Piggy Stats">
    <div class="space-y-4">
      <UserBalance />
      <LeaderboardProgress />
    </div>
  </BasePanel>
</template>
