<!-- src/components/pig-designer/FeatureSelector.vue -->
<script setup lang="ts">
import { computed, ref } from 'vue';
import type { PigFeature } from '@/types/pig';

const props = defineProps<{
  label: string;
  parts: PigFeature[];
  modelValue: string;
  allowNone?: boolean;
  icon?: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const isExpanded = ref(false);

const enabledParts = computed<PigFeature[]>(() => {
  const filtered = props.parts.filter((part) => part.enabled !== false);

  // Add "None" option if allowNone is true
  if (props.allowNone) {
    return [
      {
        id: '',
        name: 'None',
        path: '',
        connectionPoints: {},
        enabled: true,
      },
      ...filtered,
    ];
  }

  return filtered;
});

const selectedPart = computed<PigFeature | undefined>(() => {
  const part = enabledParts.value.find((part) => part.id === props.modelValue);
  // If no part is selected and allowNone is true, return the "None" option
  if (!part && props.allowNone) {
    return {
      id: '',
      name: 'None',
      path: '',
      enabled: true,
    };
  }
  return part;
});

function selectPart(partId: string) {
  emit('update:modelValue', partId);
  isExpanded.value = false;
}

function toggleExpanded() {
  isExpanded.value = !isExpanded.value;
}
</script>

<template>
  <div
    class="rounded-lg border border-pink-100 bg-white shadow-sm transition-all"
    :class="{ 'ring-2 ring-pink-200': isExpanded }"
  >
    <!-- Header -->
    <div
      @click="toggleExpanded"
      class="flex cursor-pointer items-center justify-between rounded-t-lg px-4 py-3 hover:bg-pink-50"
    >
      <div class="flex items-center">
        <span v-if="icon" class="mr-2 text-lg">{{ icon }}</span>
        <h3 class="font-medium text-gray-700">{{ label }}</h3>
      </div>
      <!-- Chevron Icon - animated rotate 180 degrees on expanded state of our parts drawer  -->
      <!-- div with text-color class lets use fill="currentColor" in the chevron to avoid defining a custom color -->
      <div class="text-pink-500">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 transition-transform duration-200"
          :class="{ 'rotate-180 transform': isExpanded }"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clip-rule="evenodd"
          />
        </svg>
      </div>
    </div>

    <!-- Selected part (when collapsed) -->
    <div
      v-if="!isExpanded && selectedPart"
      class="flex items-center border-t border-pink-100 px-4 py-3"
      @click="toggleExpanded"
    >
      <div
        class="mr-3 flex h-14 w-14 items-center justify-center overflow-hidden rounded-lg border border-pink-100 bg-gray-50 shadow-sm"
      >
        <img
          v-if="selectedPart.path"
          :src="selectedPart.path"
          :alt="selectedPart.name"
          class="h-10 w-10 object-contain"
        />
        <div v-else class="text-xs text-gray-400">No image</div>
      </div>
      <div class="flex-1">
        <div class="font-medium text-gray-800">{{ selectedPart.name }}</div>
        <div class="mt-1 text-xs text-pink-500">Click to change</div>
      </div>
    </div>

    <!-- All parts (when expanded) -->
    <div
      v-if="isExpanded"
      class="rounded-b-lg border-t border-pink-100 bg-gray-50 p-3"
    >
      <div class="grid grid-cols-2 gap-3">
        <div
          v-for="part in enabledParts"
          :key="part.id"
          @click="selectPart(part.id)"
          class="flex cursor-pointer flex-col items-center rounded-lg p-2 transition-all hover:bg-white"
          :class="{ 'bg-white ring-2 ring-pink-400': part.id === modelValue }"
        >
          <div
            class="mb-2 flex h-14 w-14 items-center justify-center overflow-hidden rounded-lg border border-pink-100 bg-white shadow-sm"
          >
            <img
              v-if="part.path"
              :src="part.path"
              :alt="part.name"
              class="h-10 w-10 object-contain"
            />
            <div v-else class="text-center text-xs text-gray-400">None</div>
          </div>
          <div class="text-center text-sm font-medium text-gray-700">
            {{ part.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
