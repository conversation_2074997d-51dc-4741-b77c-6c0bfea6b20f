﻿
namespace PigGameLibrary.Databases
{
    public interface ISqlDataAccess
    {
        Task<List<T>> LoadData<T, U>(string sqlStatement, U parameters, string connectionStringName, bool isStoredProcedure = false);
        Task SaveData<T>(string sqlStatement, T parameters, string connectionStringName, bool isStoredProcedure = false);
        Task<int> ExecuteWithRowCount<T>(string sqlStatement, T parameters, string connectionStringName, bool isStoredProcedure = false);
    }
}