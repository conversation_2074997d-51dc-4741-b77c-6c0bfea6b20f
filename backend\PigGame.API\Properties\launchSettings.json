﻿{
  "$schema": "https://json.schemastore.org/launchsettings.json",
  "profiles": {
    "http": {
        "commandName": "Project",
        "dotnetRunMessages": true,
        "launchBrowser": true,
        "launchUrl": "swagger",
        "applicationUrl": "http://localhost:5018",
        "environmentVariables": {
            "ASPNETCORE_ENVIRONMENT": "Development"
        },
        "windowsAuthentication": true,
        "anonymousAuthentication": false
    },
    "https": {
        "commandName": "Project",
        "dotnetRunMessages": true,
        "launchBrowser": true,
        "launchUrl": "swagger",
        "applicationUrl": "https://localhost:7057;http://localhost:5018",
        "environmentVariables": {
            "ASPNETCORE_ENVIRONMENT": "Development"
        },
        "windowsAuthentication": true,
        "anonymousAuthentication": false
    }
  }
}
