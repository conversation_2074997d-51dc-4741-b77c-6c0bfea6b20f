<script setup lang="ts">
import { useAuthStore } from '@/stores/authStore';
import BasePanel from '@/components/common/BasePanel.vue';

const authStore = useAuthStore();
</script>

<template>
  <div>
    <h2 class="mb-4 text-2xl font-bold">Admin Dashboard</h2>

    <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
      <BasePanel>
        <h3 class="mb-3 text-xl font-semibold">User Tasks</h3>
        <p class="mb-4">Manage user tasks and rewards</p>
        <router-link
          :to="{ name: 'AdminUserTasks' }"
          class="inline-block rounded-lg bg-pink-500 px-4 py-2 text-white transition-colors hover:bg-pink-600"
        >
          Manage Tasks
        </router-link>
      </BasePanel>

      <BasePanel v-if="authStore.isSuperAdmin">
        <h3 class="mb-3 text-xl font-semibold">User Management</h3>
        <p class="mb-4">Manage users and their roles</p>
        <router-link
          :to="{ name: 'AdminUsers' }"
          class="inline-block rounded-lg bg-pink-500 px-4 py-2 text-white transition-colors hover:bg-pink-600"
        >
          Manage Users
        </router-link>
      </BasePanel>

      <BasePanel v-if="authStore.isSuperAdmin">
        <h3 class="mb-3 text-xl font-semibold">Logging</h3>
        <p class="mb-4">View application logs</p>
        <router-link
          :to="{ name: 'AdminLogs' }"
          class="inline-block rounded-lg bg-pink-500 px-4 py-2 text-white transition-colors hover:bg-pink-600"
        >
          View Logs
        </router-link>
      </BasePanel>
    </div>
  </div>
</template>
