CREATE PROCEDURE [dbo].[spCreateLogEntry]
    @Timestamp DATETIME2,
    @Level NVARCHAR(10),
    @Message NVARCHAR(MAX),
    @Source NVARCHAR(255),
    @UserId INT = NULL,
    @UserName NVARCHAR(100) = NULL,
    @SessionId NVARCHAR(100) = NULL,
    @ContextJson NVARCHAR(MAX) = NULL,
    @StackTrace NVARCHAR(MAX) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO Logs (
        Timestamp, 
        Level, 
        Message, 
        Source, 
        UserId, 
        UserName, 
        SessionId, 
        ContextJson, 
        StackTrace
    )
    VALUES (
        @Timestamp, 
        @Level, 
        @Message, 
        @Source, 
        @UserId, 
        @UserName, 
        @SessionId, 
        @ContextJson, 
        @StackTrace
    );
    
    SELECT SCOPE_IDENTITY() AS Id;
END
