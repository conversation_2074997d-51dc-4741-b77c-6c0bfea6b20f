<script setup lang="ts">
import { onMounted, computed } from 'vue';
import UserPig from '../pig-designer/UserPig.vue';
import { useUserStore } from '@/stores/userStore';

const userStore = useUserStore();

onMounted(async () => {
  await userStore.loadLeaderboard();
});

const top3Users = computed(() => {
  return userStore.leaderboardUsers?.slice(0, 3) ?? [];
});
</script>

<template>
  <div
    class="relative min-h-[500px] overflow-hidden rounded-xl bg-gradient-to-b from-blue-50 to-green-100 p-8 shadow-lg"
  >
    <!-- Background elements - TODO: replace with something better  -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Grass -->
      <div class="absolute right-0 bottom-0 left-0 z-0 h-40 bg-green-200"></div>
      <!-- <PERSON>ce posts -->
      <div
        class="absolute right-0 bottom-0 left-0 z-0 flex h-48 items-end justify-around"
      >
        <div
          v-for="i in 8"
          :key="i"
          class="h-32 w-4 rounded-t-lg bg-yellow-800"
        ></div>
      </div>
      <!-- Fence top rail -->
      <div
        class="absolute right-0 bottom-32 left-0 z-0 h-4 bg-yellow-700"
      ></div>
      <!-- Sun -->
      <div
        class="absolute top-6 right-6 h-16 w-16 rounded-full bg-yellow-300 opacity-70"
      ></div>
      <!-- Clouds -->
      <div
        class="absolute top-12 left-12 h-12 w-24 rounded-full bg-white opacity-60"
      ></div>
      <div
        class="absolute top-24 left-36 h-14 w-32 rounded-full bg-white opacity-50"
      ></div>
    </div>
    <!-- end placeholder background elements -->

    <!-- Leaderboard title -->
    <div class="relative z-10 mb-8 text-center">
      <h2 class="text-3xl font-bold text-blue-800">Top Piggies</h2>
      <p class="text-blue-600">Meet our leading pigs!</p>
    </div>

    <!-- First place (center, largest) -->
    <div class="absolute top-1/3 left-1/2 z-30 -translate-x-1/2 transform">
      <!-- First place ribbon -->
      <div
        class="absolute -top-20 left-1/2 z-40 h-28 w-24 -translate-x-1/2 transform"
      >
        <div
          class="relative flex h-20 w-full items-center justify-center rounded-t-lg bg-blue-600"
        >
          <span class="text-3xl font-bold text-white">1</span>
          <div
            class="absolute -bottom-6 left-0 h-0 w-0 border-t-[24px] border-l-[48px] border-t-blue-600 border-l-transparent"
          ></div>
          <div
            class="absolute right-0 -bottom-6 h-0 w-0 border-t-[24px] border-r-[48px] border-t-blue-600 border-r-transparent"
          ></div>
        </div>
      </div>

      <div class="rounded-xl border-4 border-blue-600 bg-white p-4 shadow-xl">
        <!-- UserPig component for 1st place -->
        <div class="relative h-48 w-48">
          <div class="absolute inset-0 translate-y-[-50%] transform">
            <UserPig size="large" :pigId="top3Users[0]?.userId" />
          </div>
        </div>
        <div class="mt-2 text-center">
          <p class="text-lg font-bold">{{ top3Users[0]?.pigName }}</p>
          <p class="text-gray-600">
            {{ top3Users[0]?.displayName }} ({{ top3Users[0]?.coins }} pts)
          </p>
        </div>
      </div>
    </div>

    <!-- Second place (left of 1st) -->
    <div class="absolute top-1/2 left-1/4 z-20 -translate-x-1/2 transform">
      <!-- Second place ribbon -->
      <div
        class="absolute -top-16 left-1/2 z-40 h-24 w-20 -translate-x-1/2 transform"
      >
        <div
          class="relative flex h-16 w-full items-center justify-center rounded-t-lg bg-gray-400"
        >
          <span class="text-2xl font-bold text-white">2</span>
          <div
            class="absolute -bottom-4 left-0 h-0 w-0 border-t-[16px] border-l-[40px] border-t-gray-400 border-l-transparent"
          ></div>
          <div
            class="absolute right-0 -bottom-4 h-0 w-0 border-t-[16px] border-r-[40px] border-t-gray-400 border-r-transparent"
          ></div>
        </div>
      </div>

      <div class="rounded-xl border-2 border-gray-400 bg-white p-3 shadow-lg">
        <!-- UserPig component for 2nd place -->
        <div class="relative h-32 w-32">
          <div class="absolute inset-0 translate-y-[-50%] transform">
            <UserPig size="medium" :pigId="top3Users[1]?.userId" />
          </div>
        </div>
        <div class="mt-2 text-center">
          <p class="font-bold">{{ top3Users[1]?.pigName }}</p>
          <p class="text-sm text-gray-600">
            {{ top3Users[1]?.displayName }} ({{ top3Users[1]?.coins }} pts)
          </p>
        </div>
      </div>
    </div>

    <!-- Third place (right of 1st) -->
    <div class="absolute top-1/2 left-3/4 z-20 -translate-x-1/2 transform">
      <!-- Third place ribbon -->
      <div
        class="absolute -top-16 left-1/2 z-40 h-24 w-20 -translate-x-1/2 transform"
      >
        <div
          class="relative flex h-16 w-full items-center justify-center rounded-t-lg bg-yellow-600"
        >
          <span class="text-2xl font-bold text-white">3</span>
          <div
            class="absolute -bottom-4 left-0 h-0 w-0 border-t-[16px] border-l-[40px] border-t-yellow-600 border-l-transparent"
          ></div>
          <div
            class="absolute right-0 -bottom-4 h-0 w-0 border-t-[16px] border-r-[40px] border-t-yellow-600 border-r-transparent"
          ></div>
        </div>
      </div>

      <div class="rounded-xl border-2 border-yellow-600 bg-white p-3 shadow-lg">
        <!-- UserPig component for 3rd place -->
        <div class="relative h-32 w-32">
          <div class="absolute inset-0 translate-y-[-50%] transform">
            <UserPig size="medium" :pigId="top3Users[2]?.userId" />
          </div>
        </div>
        <div class="mt-2 text-center">
          <p class="font-bold">{{ top3Users[2]?.pigName }}</p>
          <p class="text-sm text-gray-600">
            {{ top3Users[2]?.displayName }} ({{ top3Users[2]?.coins }} pts)
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
