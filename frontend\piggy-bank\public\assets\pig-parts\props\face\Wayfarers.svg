<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 130 60" width="130" height="60">
<g
  id='Top_Resources_Wayfarers'
  fill='none'
  strokeWidth='1'>
  <defs>
    <filter
      x='-0.8%'
      y='-2.4%'
      width='101.6%'
      height='109.8%'
      filterUnits='objectBoundingBox'
      id='filter1'>
      <feOffset
        dx='0'
        dy='2'
        in='SourceAlpha'
        result='shadowOffsetOuter1'
      />
      <feColorMatrix
        values='0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.2 0'
        type='matrix'
        in='shadowOffsetOuter1'
        result='shadowMatrixOuter1'
      />
      <feMerge>
        <feMergeNode in='shadowMatrixOuter1' />
        <feMergeNode in='SourceGraphic' />
      </feMerge>
    </filter>
    <linearGradient
      x1='50%'
      y1='0%'
      x2='50%'
      y2='100%'
      id='linearGradient1'>
      <stop stop-color='#FFFFFF' stop-opacity='0.5' offset='0%' />
      <stop stop-color='#000000' stop-opacity='0.5' offset='70.5058195%' />
    </linearGradient>
    <path
      d='M44.9178864,17.5714286 C44.9178864,27.2737857 36.66775,35.1428571 22.9204545,35.1428571 L20.1704091,35.1428571 C6.42311364,35.1428571 0.923022727,27.2708571 0.923022727,17.5714286 L0.923022727,17.5714286 C0.923022727,7.86614286 2.20715909,0 21.4545455,0 L24.3863636,0 C43.63375,0 44.9178864,7.86614286 44.9178864,17.5714286 L44.9178864,17.5714286 Z'
      id='path1'
    />
    <path
      d='M106.486068,17.5714286 C106.486068,27.2737857 98.2388636,35.1428571 84.4886364,35.1428571 L81.7385909,35.1428571 C67.9912955,35.1428571 62.4912045,27.2708571 62.4912045,17.5714286 L62.4912045,17.5714286 C62.4912045,7.86614286 63.7753409,0 83.0227273,0 L85.9545455,0 C105.199,0 106.486068,7.86614286 106.486068,17.5714286 L106.486068,17.5714286 Z'
      id='path2'
    />
  </defs>
  <g
    id='Wayfarers'
    filter='url(#filter1)'
    transform='translate(7.000000, 7.000000)'>
    <g
      id='Shades'
      transform='translate(10.795455, 2.928571)'
      fillRule='nonzero'>
      <g id='Shade'>
        <use
          fill-opacity='0.700000048'
          fill='#000000'
          fill-rule='evenodd'
          xlink:href='#path1'
        />
        <use
          fill='url(#linearGradient1)'
          fill-rule='evenodd'
          style='mix-blend-mode: screen'
          xlink:href='#path1'
        />
      </g>
      <g id='Shade'>
        <use
          fill-opacity='0.700000048'
          fill='#000000'
          fill-rule='evenodd'
          xlink:href='#path2'
        />
        <use
          fill='url(#linearGradient1)'
          fill-rule='evenodd'
          style='mix-blend-mode: screen'
          xlink:href='#path2'
        />
      </g>
    </g>
    <path
      d='M33.7159091,41 L30.9658636,41 C17.0778409,41 8.78665909,33.3359286 8.78665909,20.5 C8.78665909,10.127 10.5985227,0 32.25,0 L35.1818182,0 C56.8332955,0 58.6451591,10.127 58.6451591,20.5 C58.6451591,32.5686429 48.3955227,41 33.7159091,41 Z M32.25,5.85421429 C14.6502955,5.85421429 14.6502955,12.3175714 14.6502955,20.5 C14.6502955,27.1800714 17.4795,35.1428571 30.9658636,35.1428571 L33.7159091,35.1428571 C44.9418409,35.1428571 52.7815227,29.1217143 52.7815227,20.5 C52.7815227,12.3175714 52.7815227,5.85421429 35.1818182,5.85421429 L32.25,5.85421429 Z'
      id='Left'
      fill='#252C2F'
      fillRule='nonzero'
    />
    <path
      d='M95.2840909,41 L92.5340455,41 C78.6460227,41 70.3548409,33.3359286 70.3548409,20.5 C70.3548409,10.127 72.1667045,0 93.8181818,0 L96.75,0 C118.401477,0 120.213341,10.127 120.213341,20.5 C120.213341,32.5686429 109.963705,41 95.2840909,41 Z M93.8181818,5.85421429 C76.2184773,5.85421429 76.2184773,12.3175714 76.2184773,20.5 C76.2184773,27.1800714 79.0506136,35.1428571 92.5340455,35.1428571 L95.2840909,35.1428571 C106.510023,35.1428571 114.349705,29.1217143 114.349705,20.5 C114.349705,12.3175714 114.349705,5.85421429 96.75,5.85421429 L93.8181818,5.85421429 Z'
      id='Right'
      fill='#252C2F'
      fillRule='nonzero'
    />
    <path
      d='M2.93181818,5.85714286 C3.61786364,5.17185714 11.1233182,0 32.25,0 C49.9640455,0 53.7138409,1.88014286 59.3898409,4.72085714 L59.8053162,4.93054903 C60.1999353,5.07314243 62.2179351,5.77419634 64.5784525,5.85128811 C66.7290156,5.75689949 68.5684809,5.16080623 69.1059926,4.96981137 C75.5844654,1.74762081 81.9260118,0 96.75,0 C117.876682,0 125.382136,5.17185714 126.068182,5.85714286 C127.689477,5.85714286 129,7.16621429 129,8.78571429 L129,11.7142857 C129,13.3337857 127.689477,14.6428571 126.068182,14.6428571 C126.068182,14.6428571 120.204545,14.6428571 120.204545,17.5714286 C120.204545,20.5 117.272727,13.3337857 117.272727,11.7142857 L117.272727,8.8618831 C113.697201,7.46243482 107.296654,5.85714286 96.75,5.85714286 C84.9995538,5.85714286 79.1475515,6.98813142 74.1276604,9.10414393 L74.1837955,9.24257143 L71.6878772,10.2500422 L74.1813177,11.2582547 L71.981173,16.6874536 L69.263564,15.5885995 C69.0208516,15.4904597 68.4971539,15.3141463 67.770994,15.1309826 C65.7466083,14.6203594 63.6653786,14.4649153 61.8248214,14.8513001 C61.1495627,14.993056 60.5230576,15.2057795 59.9480988,15.4931011 L57.3260941,16.8033836 L54.7026238,11.5651815 L57.3246285,10.2548989 L57.3310023,10.251716 L54.8191364,9.23671429 L54.8992448,9.03890561 C50.5700368,6.97578666 46.5781927,5.85714286 32.25,5.85714286 C21.7038986,5.85714286 15.3034993,7.46145875 11.7272727,8.86093383 L11.7272727,11.7142857 C11.7272727,13.3337857 8.79545455,20.5 8.79545455,17.5714286 C8.79545455,14.6428571 2.93181818,14.6428571 2.93181818,14.6428571 C1.31345455,14.6428571 0,13.3337857 0,11.7142857 L0,8.78571429 C0,7.16621429 1.31345455,5.85714286 2.93181818,5.85714286 Z'
      id='Stuff'
      fill='#252C2F'
      fillRule='nonzero'
    />
  </g>
</g>
</svg>