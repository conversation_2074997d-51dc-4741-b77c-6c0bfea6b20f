﻿using Microsoft.Identity.Client;
using Microsoft.VisualBasic.FileIO;
using PigGameLibrary.Databases;
using PigGameLibrary.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PigGameLibrary.Data
{
    public class TaskData : ITaskData
    {
        private readonly ISqlDataAccess _db;
        private const string connectionStringName = "SqlDb";

        public TaskData(ISqlDataAccess db)
        {
            _db = db;
        }

        public Task<List<TaskModel>> GetAllTasks()
        {
            return _db.LoadData<TaskModel, dynamic>(
                "dbo.spGetAllTasks", new { }, connectionStringName, true
                );
        }

        public async Task<TaskModel?> GetTaskById(int id)
        {
            var results = await _db.LoadData<TaskModel, dynamic>(
                "dbo.spTaskById",
                new { Id = id },
                connectionStringName,
                true
                );

            return results.FirstOrDefault();
        }

        public async Task DeleteTaskById(int id)
        {
            await _db.SaveData("dbo.spDeleteTaskById",
                new { Id = id },
                connectionStringName,
                true);
        }

        public async Task<TaskModel?> CreateTask(string name, string description, int coins)
        {
            var results = await _db.LoadData<TaskModel, dynamic>("dbo.spCreateNewTask",
                new { Name = name, Description = description, CoinReward = coins },
                connectionStringName, true);

            return results.FirstOrDefault();
        }

        public async Task UpdateTask(int id, string name, string description, int coins)
        {
            await _db.SaveData("dbo.spUpdateTask",
                new { Id = id, Name = name, Description = description, CoinReward = coins },
                connectionStringName, true);
        }
    }
}
