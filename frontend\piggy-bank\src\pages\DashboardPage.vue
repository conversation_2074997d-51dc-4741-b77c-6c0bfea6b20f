<!-- src/pages/DashboardPage.vue -->
<script setup lang="ts">
import PageHeader from '@/components/common/PageHeader.vue';
import PigDisplay from '@/components/dashboard/PigDisplay.vue';
import StatsPanel from '@/components/dashboard/StatsPanel.vue';
import NotificationList from '@/components/dashboard/NotificationList.vue';
import { useUserStore } from '@/stores/userStore';

const userStore = useUserStore();
</script>

<template>
  <div class="p-8">
    <PageHeader
      title="Dashboard"
      :subTitle="`Welcome, ${userStore.firstName}!`"
    />

    <!-- <h1 class="mb-8 text-2xl">Welcome, {{ userStore.firstName }}</h1> -->
    <div class="grid grid-cols-12 gap-6">
      <!-- Left side - Pig Display -->
      <div class="col-span-6">
        <PigDisplay />
      </div>
      <!-- Right side - Stats -->
      <div class="col-span-6">
        <StatsPanel />
      </div>
      <!-- Bottom - Notifcations -->
      <div class="col-span-12">
        <NotificationList />
      </div>
    </div>
  </div>
</template>
