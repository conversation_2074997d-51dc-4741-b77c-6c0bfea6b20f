{"name": "piggy-bank", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:stage": "vue-tsc -b && vite build --mode staging", "build:prod": "vue-tsc -b && vite build --mode production", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.2", "@vue/cli": "^5.0.8", "axios": "^1.6.2", "pinia": "^2.3.1", "tailwindcss": "^4.0.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "@vue/eslint-config-typescript": "^14.3.0", "@vue/tsconfig": "^0.7.0", "cross-env": "^7.0.3", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "globals": "^15.14.0", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.6.2", "typescript-eslint": "^8.22.0", "vite": "^6.0.5", "vue-tsc": "^2.2.0"}}