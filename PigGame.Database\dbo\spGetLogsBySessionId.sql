CREATE PROCEDURE [dbo].[spGetLogsBySessionId]
    @SessionId NVARCHAR(100),
    @Limit INT = 100
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@Limit)
        Id,
        Timestamp,
        Level,
        Message,
        Source,
        UserId,
        UserName,
        SessionId,
        ContextJson,
        StackTrace
    FROM 
        Logs
    WHERE 
        SessionId = @SessionId
    ORDER BY 
        Timestamp DESC;
END
