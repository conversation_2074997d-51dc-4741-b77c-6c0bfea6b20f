﻿CREATE PROCEDURE [dbo].[spEditCoinsUser]
    @UserId INT,
    @Coins INT
AS
BEGIN
SET NOCOUNT ON;

	IF NOT EXISTS (SELECT 1 FROM Users WHERE Id = @UserId)
	BEGIN
		RAISERROR('User does not exist',16,1);
		RETURN;
	END
	IF EXISTS (SELECT 1 FROM Users WHERE Id = @UserId AND (UnclaimedCoins + @Coins) >= 0)
    BEGIN
		UPDATE Users
		SET UnclaimedCoins = ISNULL(UnclaimedCoins, 0) + @Coins
		WHERE Id = @UserId;
	END
	ELSE
	BEGIN
	RAISERROR('Not enough coins to subtract from',16,1);
	END
END
