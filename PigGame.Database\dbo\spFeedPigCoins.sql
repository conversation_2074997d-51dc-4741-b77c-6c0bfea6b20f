CREATE PROCEDURE [dbo].[spFeedPigCoins]
    @UserId INT,
    @CoinsToFeed INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @OldCoins INT;
    DECLARE @NewCoins INT;
    DECLARE @OldRank INT = NULL;
    DECLARE @NewRank INT = NULL;
    DECLARE @OldLevel INT = NULL;
    DECLARE @NewLevel INT = NULL;
    DECLARE @LevelUpMessage NVARCHAR(255);
    DECLARE @LevelUpMetadata NVARCHAR(MAX);
    DECLARE @RewardTierId INT;
    DECLARE @RewardLevel INT;
    DECLARE @CashReward NVARCHAR(50);
    
    -- Get current user coins and rank
    SELECT @OldCoins = Coins FROM Users WHERE Id = @UserId;
    
    -- Verify user has enough unclaimed coins
    IF EXISTS (SELECT 1 FROM Users WHERE Id = @UserId AND UnclaimedCoins >= @CoinsToFeed)
    BEGIN
        -- Update both coin values in a single transaction
        BEGIN TRANSACTION;
        
        UPDATE Users
        SET UnclaimedCoins = UnclaimedCoins - @CoinsToFeed,
            Coins = Coins + @CoinsToFeed
        WHERE Id = @UserId;
        
        -- Get new coin total
        SET @NewCoins = @OldCoins + @CoinsToFeed;
        
        -- Check for level up
        SELECT TOP 1 
            @OldLevel = Level
        FROM 
            RewardTiers
        WHERE 
            CoinsRequired <= @OldCoins
        ORDER BY 
            CoinsRequired DESC;
        
        SELECT TOP 1 
            @NewLevel = Level,
            @RewardTierId = Id,
            @RewardLevel = Level,
            @CashReward = CashReward
        FROM 
            RewardTiers
        WHERE 
            CoinsRequired <= @NewCoins
        ORDER BY 
            CoinsRequired DESC;
        
        -- If level changed, record activity
        IF (@NewLevel > @OldLevel OR (@OldLevel IS NULL AND @NewLevel IS NOT NULL))
        BEGIN
            SET @LevelUpMessage = 'You reached Level ' + CAST(@NewLevel AS NVARCHAR) + ' and earned ' + @CashReward + '!';
            SET @LevelUpMetadata = '{"level":' + CAST(@NewLevel AS NVARCHAR) + ',"reward":"' + @CashReward + '","coins":' + CAST(@NewCoins AS NVARCHAR) + '}';
            
            EXEC spCreateUserActivity 
                @UserId = @UserId,
                @Type = 'level_up',
                @Message = @LevelUpMessage,
                @MetadataJson = @LevelUpMetadata;
        END
        
        COMMIT TRANSACTION;
    END
    ELSE
    BEGIN
        RAISERROR('Not enough unclaimed coins available', 16, 1);
    END
END
