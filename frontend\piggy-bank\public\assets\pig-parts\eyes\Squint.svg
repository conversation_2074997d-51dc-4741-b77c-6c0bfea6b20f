<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130 60" width="130" height="60">
  <g id="Eyes/Squint-😊" transform="translate(0.000000, 8.000000)">
    <defs>
      <path d="M14,14.0481187 C23.6099827,14.0481187 28,18.4994466 28,11.5617716 C28,4.62409673 21.7319865,0 14,0 C6.2680135,0 0,4.62409673 0,11.5617716 C0,18.4994466 4.39001726,14.0481187 14,14.0481187 Z" id="react-path-33"></path>
      <path d="M14,14.0481187 C23.6099827,14.0481187 28,18.4994466 28,11.5617716 C28,4.62409673 21.7319865,0 14,0 C6.2680135,0 0,4.62409673 0,11.5617716 C0,18.4994466 4.39001726,14.0481187 14,14.0481187 Z" id="react-path-34"></path>
    </defs>
    <g id="Eye" transform="translate(16.000000, 13.000000)">
      <mask id="react-mask-35" fill="white">
        <use href="#react-path-33"></use>
      </mask>
      <use id="The-white-stuff" fill="#FFFFFF" href="#react-path-33"></use>
      <circle fill-opacity="0.699999988" fill="#000000" mask="url(#react-mask-35)" cx="14" cy="10" r="6"></circle>
    </g>
    <g id="Eye" transform="translate(78.000000, 13.000000)">
      <mask id="react-mask-36" fill="white">
        <use href="#react-path-34"></use>
      </mask>
      <use id="Eyeball-Mask" fill="#FFFFFF" href="#react-path-34"></use>
      <circle fill-opacity="0.699999988" fill="#000000" mask="url(#react-mask-36)" cx="14" cy="10" r="6"></circle>
    </g>
  </g>
</svg>