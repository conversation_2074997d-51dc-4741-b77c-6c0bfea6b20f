﻿using PigGameLibrary.Models;

namespace PigGameLibrary.Data
{
    public interface IUserData
    {
        Task<UserModel?> Create<PERSON>ser(string username, string displayname, string email, string role = "User");
        Task<int> DeleteUserById(int id);
        Task<int> EditUserCoins(int userId, int coins);
        Task<List<UserModel>> GetAllUsers();
        Task<UserModel?> GetUserById(int id);
        Task<UserModel?> GetUserByName(string username);
        Task<List<LeaderboardModel>> GetLeaderboardData();
        Task FeedPigCoins(int userId, int coinsToFeed);
        Task<UserModel?> UpdateUserLastLogin(string username);
        Task<int> UpdateUserRole(int userId, string role);
    }
}
