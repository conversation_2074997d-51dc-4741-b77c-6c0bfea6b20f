<script setup lang="ts">
interface Props {
  amount: number;
}

defineProps<Props>();
</script>

<template>
  <div class="coin-container">
    <span class="coin-value">{{ amount }}</span>
  </div>
</template>

<style scoped>
.coin-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ffeb3b !important;
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  font-weight: 900;
  color: #000;
  border: 2px solid #000;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.coin-container:hover {
  transform: scale(1.1) rotate(15deg);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.coin-value {
  font-size: 1.1em;
  font-weight: 900;
}
</style>