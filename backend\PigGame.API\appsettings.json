{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "PigGame.API": "Debug"}}, "AllowedHosts": "*", "ConnectionStrings": {"SqlDb": "Data Source=(localdb)\\MSSQLLocalDB;Initial Catalog=PigGame.Database;Integrated Security=True;Connect Timeout=30;Encrypt=False;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False"}, "VirtualDirectory": "/", "LoggingSettings": {"RetentionDays": 30, "EnablePIIMasking": true, "MinimumLevel": "Information"}}