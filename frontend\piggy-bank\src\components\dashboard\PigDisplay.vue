<!-- src/components/dashboard/PigDisplay.vue -->
<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref } from 'vue';
import { usePigStore } from '@/stores/pigStore';
import BasePanel from '@/components/common/BasePanel.vue';
import UserPig from '@/components/pig-designer/UserPig.vue';

const router = useRouter();

const pigStore = usePigStore();
const pigName = ref(pigStore.selectedPig.name || '');

const goToDesigner = () => {
  router.push({ name: 'PigDesigner' });
};
</script>

<template>
  <BasePanel>
    <div class="mb-4 flex items-center justify-between">
      <button
        @click="goToDesigner"
        class="rounded-lg bg-gradient-to-r from-pink-500 to-rose-400 px-6 py-3 font-medium text-white shadow-md transition-all hover:-translate-y-0.5 hover:from-pink-600 hover:to-rose-500 hover:shadow-lg focus:ring-2 focus:ring-pink-300 focus:outline-none"
      >
        Edit Pig
      </button>
      <h2 class="mb-4 text-xl">{{ pigName }}</h2>
    </div>
    <div class="farm-scene relative h-64 overflow-hidden">
      <div class="bg-farm-scene absolute inset-0"></div>
      <div class="pig-container absolute">
        <div class="pig-shadow"></div>
        <UserPig size="medium" class="z-10 scale-75" />
      </div>
    </div>
  </BasePanel>
</template>

<style scoped>
.bg-farm-scene {
  background-image: url('/assets/backgrounds/2-farm-life-bg.png');
  background-position: center 85%;
}

.pig-container {
  left: 40%;
  bottom: 2px;
}

.pig-shadow {
  height: 30px;
  width: 150px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  filter: blur(3px);
  margin: 0 auto;
  position: absolute;
  bottom: 20px;
  left: 27%;
  z-index: 5;
}
</style>
