{"version": "0.2.0", "configurations": [{"name": "Backend API", "type": "coreclr", "request": "launch", "preLaunchTask": "build", "program": "${workspaceFolder}/backend/PigGame.API/bin/Debug/net9.0/PigGame.API.dll", "args": [], "cwd": "${workspaceFolder}/backend/PigGame.API", "stopAtEntry": false, "serverReadyAction": {"action": "openExternally", "pattern": "\\bNow listening on:\\s+(https?://\\S+)", "uriFormat": "%s/swagger"}, "env": {"ASPNETCORE_ENVIRONMENT": "Development"}, "console": "internalConsole", "internalConsoleOptions": "openOnSessionStart"}, {"name": "Frontend (npm run dev)", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend/piggy-bank", "runtimeExecutable": "npm", "runtimeArgs": ["run", "dev"], "serverReadyAction": {"pattern": "Local:.+(https?://\\S+)", "uriFormat": "%s", "action": "openExternally"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"name": "Frontend with Delay", "type": "node", "request": "launch", "cwd": "${workspaceFolder}/frontend/piggy-bank", "runtimeExecutable": "node", "runtimeArgs": ["-e", "setTimeout(() => { require('child_process').spawn('npm', ['run', 'dev'], { stdio: 'inherit', shell: true }) }, 8000)"], "serverReadyAction": {"pattern": "Local:.+(https?://\\S+)", "uriFormat": "%s", "action": "openExternally"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}], "compounds": [{"name": "Full Stack", "configurations": ["Backend API", "Frontend with Delay"], "stopAll": true}, {"name": "Full Stack (No Delay)", "configurations": ["Backend API", "Frontend (npm run dev)"], "stopAll": true}]}