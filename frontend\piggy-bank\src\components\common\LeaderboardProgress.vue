<script setup lang="ts">
import { onMounted } from 'vue';
import { useUserStore } from '@/stores/userStore';

const userStore = useUserStore();

onMounted(async () => {
  await userStore.loadLeaderboard();
});
</script>

<template>
  <!-- Leaderboard progress -->
  <div class="mt-6 mb-4">
    <div class="mb-1 flex justify-between text-sm text-gray-600">
      <span>Current Rank: {{ userStore.currentRank }}</span>
      <span
        v-if="
          userStore.coinsToNextRank !== null && userStore.coinsToNextRank > 0
        "
      >
        {{ userStore.coinsToNextRank }} coins to #{{
          (userStore.currentRank ?? 0) - 1
        }}
      </span>
      <span v-else-if="userStore.currentRank === 1"> You're #1! </span>
      <span v-else> Tied for #{{ userStore.currentRank }} </span>
    </div>
    <div class="h-3 overflow-hidden rounded-full bg-gray-200">
      <div
        class="h-full bg-pink-500 transition-all"
        :style="{ width: userStore.progressToNextRank + '%' }"
      ></div>
    </div>
  </div>

  <!-- Top 5 Status -->
  <div class="mt-4 rounded-lg bg-blue-50 p-4">
    <div class="mb-1 text-center font-medium text-blue-700">Top 5 Progress</div>
    <div class="text-sm text-blue-600">
      <span v-if="!userStore.isInTopFive && userStore.coinsToTopFive">
        You need {{ userStore.coinsToTopFive }} more coins to enter the Top 5!
      </span>
      <span v-else>You're already in the Top 5! Keep it up!</span>
    </div>
  </div>

  <!-- Pig Stats -->
  <div class="mt-6 grid grid-cols-3 gap-4">
    <div class="rounded-lg bg-pink-50 p-3 text-center">
      <div class="text-xl font-bold text-pink-700">
        {{ userStore.coins }}
      </div>
      <div class="text-sm text-gray-600">Coins Fed</div>
    </div>

    <div class="rounded-lg bg-pink-50 p-3 text-center">
      <div class="text-xl font-bold text-pink-700">
        #{{ userStore.currentRank || '?' }}
      </div>
      <div class="text-sm text-gray-600">Rank</div>
    </div>

    <div class="rounded-lg bg-pink-50 p-3 text-center">
      <div class="text-xl font-bold text-pink-700">
        {{
          userStore.coinsToNextRank !== null && userStore.coinsToNextRank > 0
            ? userStore.coinsToNextRank
            : 0
        }}
      </div>
      <div class="text-sm text-gray-600">To Next Rank</div>
    </div>
  </div>
</template>
