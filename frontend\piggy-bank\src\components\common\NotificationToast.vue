<script setup lang="ts">
import { ref, onMounted } from 'vue';

interface Props {
  message: string;
  type: 'success' | 'error';
  duration?: number;
}

const props = withDefaults(defineProps<Props>(), {
  duration: 3000,
});

const emit = defineEmits(['close']);
const isVisible = ref(false);

onMounted(() => {
  setTimeout(() => {
    isVisible.value = true;
  }, 100);
  setTimeout(() => {
    isVisible.value = false;
    setTimeout(() => emit('close'), 300);
  }, props.duration);
});
</script>

<template>
  <div
    class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transition-all duration-300 w-fit"
    :class="{
      'translate-x-0 opacity-100': isVisible,
      'translate-x-full opacity-0': !isVisible,
      'bg-green-600': type === 'success',
      'bg-red-600': type === 'error',
    }"
  >
    <div class="flex items-center">
      <span v-if="type === 'success'" class="text-2xl mr-2">🎉</span>
      <span v-else class="text-2xl mr-2">❌</span>
      <p class="text-white">{{ message }}</p>
    </div>
  </div>
</template>
