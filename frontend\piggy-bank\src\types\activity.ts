export type ActivityType =
  | 'achievement'
  | 'coins'
  | 'level_up'
  | 'task'
  | 'leaderboard_change';

export interface Activity {
  id: string;
  type: ActivityType;
  message: string;
  timestamp: Date;
  metadata?: {
    coins?: number;
    level?: number;
    reward?: string;
    oldRank?: number;
    newRank?: number;
    taskName?: string;
  };
}

export interface UserActivityResponse {
  id: number;
  userId: number;
  type: ActivityType;
  message: string;
  timestamp: string; // ISO date string
  metadataJson: string | null;
}
