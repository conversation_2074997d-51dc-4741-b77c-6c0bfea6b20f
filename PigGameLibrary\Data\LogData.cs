using Microsoft.Extensions.Configuration;
using PigGameLibrary.Databases;
using PigGameLibrary.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PigGameLibrary.Data
{
    public class LogData : ILogData
    {
        private readonly ISqlDataAccess _db;
        private readonly string connectionStringName = "SqlDb";

        public LogData(ISqlDataAccess db)
        {
            _db = db;
        }

        public async Task<int> CreateLogEntry(LogEntryModel logEntry)
        {
            var parameters = new
            {
                Timestamp = logEntry.Timestamp,
                Level = logEntry.Level,
                Message = logEntry.Message,
                Source = logEntry.Source,
                UserId = logEntry.UserId,
                UserName = logEntry.UserName,
                SessionId = logEntry.SessionId,
                ContextJson = logEntry.ContextJson,
                StackTrace = logEntry.StackTrace
            };

            var result = await _db.LoadData<int, dynamic>(
                "dbo.spCreateLogEntry",
                parameters,
                connectionStringName,
                true);

            return result.Count > 0 ? result[0] : -1;
        }

        public async Task<List<LogEntryModel>> GetLogs(
            string? level = null, 
            DateTime? startDate = null, 
            DateTime? endDate = null, 
            int? userId = null, 
            string? searchText = null, 
            int page = 1, 
            int pageSize = 50,
            string? userName = null,
            string? sessionId = null)
        {
            var parameters = new
            {
                Level = level,
                StartDate = startDate,
                EndDate = endDate,
                UserId = userId,
                SearchText = searchText,
                PageNumber = page,
                PageSize = pageSize
            };

            return await _db.LoadData<LogEntryModel, dynamic>(
                "dbo.spGetLogs",
                parameters,
                connectionStringName,
                true);
        }

        public async Task<int> GetLogsCount(
            string? level = null, 
            DateTime? startDate = null, 
            DateTime? endDate = null, 
            int? userId = null, 
            string? searchText = null)
        {
            var parameters = new
            {
                Level = level,
                StartDate = startDate,
                EndDate = endDate,
                UserId = userId,
                SearchText = searchText
            };

            var result = await _db.LoadData<int, dynamic>(
                "dbo.spGetLogsCount",
                parameters,
                connectionStringName,
                true);

            return result.Count > 0 ? result[0] : 0;
        }

        public async Task<List<LogEntryModel>> GetLogsBySessionId(string sessionId, int limit = 100)
        {
            var parameters = new
            {
                SessionId = sessionId,
                Limit = limit
            };

            return await _db.LoadData<LogEntryModel, dynamic>(
                "dbo.spGetLogsBySessionId",
                parameters,
                connectionStringName,
                true);
        }

        public async Task<int> DeleteLogsBefore(DateTime date)
        {
            var parameters = new
            {
                Date = date
            };

            return await _db.ExecuteWithRowCount(
                "dbo.spDeleteLogsBefore",
                parameters,
                connectionStringName,
                true);
        }
    }
}
