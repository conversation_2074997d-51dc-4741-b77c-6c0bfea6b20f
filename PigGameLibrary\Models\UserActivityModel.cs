using System;
using System.Text.Json;

namespace PigGameLibrary.Models
{
    public class UserActivityModel
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string? MetadataJson { get; set; }

        // Helper method to get typed metadata
        public T? GetMetadata<T>() where T : class
        {
            if (string.IsNullOrEmpty(MetadataJson))
                return null;
                
            try
            {
                return JsonSerializer.Deserialize<T>(MetadataJson);
            }
            catch
            {
                return null;
            }
        }
    }

    // Activity type constants
    public static class ActivityTypes
    {
        public const string LevelUp = "level_up";
        public const string LeaderboardChange = "leaderboard_change";
        public const string TaskCompleted = "task";
        public const string CoinsEarned = "coins";
    }
}