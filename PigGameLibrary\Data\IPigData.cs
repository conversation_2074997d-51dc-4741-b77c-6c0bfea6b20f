﻿using PigGameLibrary.Models;

namespace PigGameLibrary.Data
{
    public interface IPigData
    {
        Task<PigModel> CreatePig(string name, int userId, string facePropId, string eyeId, string eyebrowId, string mouthId);
        Task<PigModel?> GetPigById(int id);
        Task<PigModel?> GetPigByUserId(int id);
        Task<bool> UpdatePig(int id, string name, string? facePropId, string? eyeId, string? eyebrowId, string? mouthId);
    }
}