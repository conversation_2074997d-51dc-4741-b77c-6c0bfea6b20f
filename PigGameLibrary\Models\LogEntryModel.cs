using System;
using System.Text.Json;

namespace PigGameLibrary.Models
{
    public class LogEntryModel
    {
        public int Id { get; set; }
        public DateTime Timestamp { get; set; }
        public string Level { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public int? UserId { get; set; }
        public string? UserName { get; set; }
        public string? SessionId { get; set; }
        public string? ContextJson { get; set; }
        public string? StackTrace { get; set; }
        
        // Helper method to get typed context
        public T? GetContext<T>() where T : class
        {
            if (string.IsNullOrEmpty(ContextJson))
                return null;
                
            try
            {
                return JsonSerializer.Deserialize<T>(Context<PERSON>son);
            }
            catch
            {
                return null;
            }
        }
    }
    
    // Log level constants
    public static class LogLevels
    {
        public const string ERROR = "ERROR";
        public const string WARN = "WARN";
        public const string INFO = "INFO";
        public const string DEBUG = "DEBUG";
        public const string TRACE = "TRACE";
    }
}
