<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';
import PageHeader from '@/components/common/PageHeader.vue';

const authStore = useAuthStore();
</script>

<template>
  <div class="p-8">
    <PageHeader title="Admin Panel" />

    <div class="mb-8 flex space-x-4">
      <RouterLink
        :to="{ name: 'AdminDashboard' }"
        :class="[
          'rounded-lg px-4 py-2 font-semibold text-white transition-colors',
          $route.name === 'AdminDashboard'
            ? 'bg-pink-500'
            : 'bg-pink-700 hover:bg-pink-600',
        ]"
      >
        Dashboard
      </RouterLink>

      <RouterLink
        :to="{ name: 'AdminUserTasks' }"
        :class="[
          'rounded-lg px-4 py-2 font-semibold text-white transition-colors',
          $route.name === 'AdminUserTasks'
            ? 'bg-pink-500'
            : 'bg-pink-700 hover:bg-pink-600',
        ]"
      >
        User Tasks
      </RouterLink>

      <RouterLink
        v-if="authStore.isSuperAdmin"
        :to="{ name: 'AdminUsers' }"
        :class="[
          'rounded-lg px-4 py-2 font-semibold text-white transition-colors',
          $route.name === 'AdminUsers'
            ? 'bg-pink-500'
            : 'bg-pink-700 hover:bg-pink-600',
        ]"
      >
        Users
      </RouterLink>

      <RouterLink
        v-if="authStore.isSuperAdmin"
        :to="{ name: 'AdminLogs' }"
        :class="[
          'rounded-lg px-4 py-2 font-semibold text-white transition-colors',
          $route.name === 'AdminLogs'
            ? 'bg-pink-500'
            : 'bg-pink-700 hover:bg-pink-600',
        ]"
      >
        System Logs
      </RouterLink>
    </div>

    <div class="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
      <RouterView />
    </div>
  </div>
</template>
