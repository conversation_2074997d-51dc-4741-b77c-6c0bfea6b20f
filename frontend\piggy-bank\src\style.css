@import 'tailwindcss';

:root {
  --hay-yellow: #f9a825;
  --theme-hay: #f9a825;
  --theme-brown-dark: #5d4037;
  --theme-brown-medium: #8d6e63;
  --theme-brown-darkest: #3e2723;
}

body {
  min-height: 100vh;
  background-image: url('/assets/BarnDoor.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3); /* Dimming overlay */
  pointer-events: none;
  z-index: 0;
}

#app {
  position: relative;
  z-index: 1;
}

/* --- Custom Utility Classes --- */
.hay-bg {
  background-color: var(--theme-hay);
  background-image: repeating-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0px,
    rgba(255, 255, 255, 0.1) 2px,
    transparent 2px,
    transparent 6px
  );
}

/* Background Colors */
.bg-theme-brown-dark {
  background-color: var(--theme-brown-dark);
}
.bg-theme-brown-medium {
  background-color: var(--theme-brown-medium);
}
.bg-theme-brown-darkest {
  background-color: var(--theme-brown-darkest);
}

/* Text Colors */
.text-theme-brown-dark {
  color: var(--theme-brown-dark);
}
.text-theme-brown-medium {
  color: var(--theme-brown-medium);
}
.text-theme-brown-darkest {
  color: var(--theme-brown-darkest);
}

/* Border Colors */
.border-theme-brown-dark {
  border-color: var(--theme-brown-dark);
}
/* Medium brown border */
.border-theme-brown-medium {
  border-color: var(--theme-brown-medium);
}
/* Darkest brown border */
.border-theme-brown-darkest {
  border-color: var(--theme-brown-darkest);
}

/* Hover States */
/* Hover state for the button */
.hover\:bg-theme-brown-darkest:hover {
  background-color: var(--theme-brown-darkest);
}
