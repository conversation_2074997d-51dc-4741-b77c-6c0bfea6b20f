<script setup lang="ts">
import { ref } from 'vue';
import PageHeader from '@/components/common/PageHeader.vue';
import TraditionalLeaderboard from '@/components/leaderboard/TraditionalLeaderboard.vue';
import PigPenLeaderboard from '@/components/leaderboard/PigPenLeaderboard.vue';

const showPigPen = ref(false);
</script>

<template>
  <div class="p-8">
    <div class="mb-8 flex items-center justify-between">
      <PageHeader
        title="Leaderboard"
        subTitle="See how you rank against others!"
      />

      <!-- Toggle Switch -->
      <div class="flex items-center gap-3 text-white">
        <span :class="{ 'font-bold': !showPigPen }">Traditional</span>
        <button
          @click="showPigPen = !showPigPen"
          class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors"
          :class="showPigPen ? 'bg-pink-500' : 'bg-gray-300'"
        >
          <span
            class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
            :class="showPigPen ? 'translate-x-6' : 'translate-x-1'"
          />
        </button>
        <span :class="{ 'font-bold': showPigPen }">Pig Pen</span>
      </div>
    </div>

    <!-- Leaderboard Display -->
    <Transition
      enter-active-class="transition-opacity duration-300"
      leave-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      leave-to-class="opacity-0"
    >
      <TraditionalLeaderboard v-if="!showPigPen" />
      <PigPenLeaderboard v-else />
    </Transition>
  </div>
</template>
