<script setup lang="ts">
import { ref, onMounted } from 'vue';
import TaskCard from '@/components/tasks/TaskCard.vue';
import NotificationToast from '@/components/common/NotificationToast.vue';
import UserBalance from '@/components/common/UserBalance.vue';
import UserPig from '@/components/pig-designer/UserPig.vue';
import PageHeader from '@/components/common/PageHeader.vue';
import BasePanel from '@/components/common/BasePanel.vue';
import { useTaskStore } from '@/stores/taskStore';
import { useUserStore } from '@/stores/userStore';
import type { TaskModel } from '@/services/taskService';

const taskStore = useTaskStore();
const userStore = useUserStore();
const loadingTaskId = ref<string | null>(null);
const notification = ref<{ message: string; type: 'success' | 'error' } | null>(
  null
);
const showPigAnimation = ref(false);
const currentSpeechBubbleMessage = ref('');

onMounted(async () => {
  await taskStore.loadTasks();
});

const speechBubbleMessages = [
  'Oink! Good job! 🐷',
  'Amazing work! ⭐',
  'Keep it up! 🌟',
  "You're crushing it! 💪",
  'Fantastic! 🎉',
  'Way to go! 🏆',
  'Brilliant work! 💫',
  "You're on fire! 🔥",
  'Super star! ⭐',
  'Outstanding! 🌟',
];

const getRandomMessage = () => {
  const randomIndex = Math.floor(Math.random() * speechBubbleMessages.length);
  return speechBubbleMessages[randomIndex];
};

const parseTaskContent = (content: string) => {
  return {
    shortDescription: content.trim(),
    detailedDescription: '',
  };
};

const handleTaskComplete = async (
  completedTask: TaskModel
) => {
  loadingTaskId.value = String(completedTask.id);

  try {
    const success = await taskStore.completeUserTask(
      completedTask.id,
      completedTask.coinReward,
      completedTask.loanAppId
    );

    if (success) {
      // Explicitly refresh user info to update the balance
      await userStore.loadUserInfo();

      notification.value = {
        message: `Task completed! Earned ${completedTask.coinReward} coins`,
        type: 'success',
      };
      currentSpeechBubbleMessage.value = getRandomMessage();
      showPigAnimation.value = true;

      setTimeout(() => {
        showPigAnimation.value = false;
      }, 3000);
    } else {
      throw new Error('Task completion failed');
    }
  } catch (error) {
    notification.value = {
      message: `An error occurred: ${error instanceof Error ? error.message : 'Please try again.'}`,
      type: 'error',
    };
  } finally {
    loadingTaskId.value = '';
  }
};

// handleToggleExpand function removed
</script>

<template>
  <div class="page-container">
    <div class="content-wrapper p-8">
      <PageHeader
        title="Tasks Area"
        subTitle="Complete a task to earn coins!"
      />

      <div class="flex items-start gap-4">
        <BasePanel title="Your Balance" class="mb-8 max-w-md">
          <UserBalance />
        </BasePanel>

        <Transition
          enter-active-class="transition-opacity duration-300"
          leave-active-class="transition-opacity duration-300"
          enter-from-class="opacity-0"
          leave-to-class="opacity-0"
        >
          <div v-if="showPigAnimation" class="pig-container">
            <div class="speech-bubble">{{ currentSpeechBubbleMessage }}</div>
            <div class="pig">
              <UserPig size="small" />
            </div>
          </div>
        </Transition>
      </div>

      <h1 class="mb-8 text-3xl font-bold text-white">Tasks</h1>

      <div class="grid max-w-[1600px] grid-cols-4 gap-4">
        <div v-for="task in taskStore.tasks" :key="task.id" class="h-fit">
          <BasePanel>
            <TaskCard
              :task="{
                id: String(task.id),
                title: task.name,
                description: parseTaskContent(task.description)
                  .shortDescription,
                reward: parseInt(String(task.coinReward), 10),
              }"
              :require-loan-app="true"
              :is-loading="loadingTaskId === String(task.id)"
              @task-complete="handleTaskComplete"
            />
          </BasePanel>
        </div>
      </div>

      <NotificationToast
        v-if="notification"
        :message="notification.message"
        :type="notification.type"
        @close="notification = null"
      />
    </div>
  </div>
</template>

<style scoped>
.content-wrapper {
  position: relative;
  z-index: 1;
}

.pig-container {
  position: relative;
  transform: translateY(80px);
  animation:
    popAndBounce 0.5s ease-out forwards,
    bounce 2s infinite 0.5s;
}

.pig {
  width: 100px;
  height: 100px;
  transform-origin: top center;
  position: relative;
  z-index: 1;
}

.speech-bubble {
  position: absolute;
  background: white;
  border-radius: 1rem;
  padding: 0.5rem 1rem;
  bottom: 40%;
  left: 80%;
  transform: translateX(-20%);
  font-weight: bold;
  border: 2px solid #5d4037;
  margin-bottom: 10px;
  animation: fadeIn 0.3s ease-out forwards;
  min-width: max-content;
  z-index: 2;
}

.speech-bubble::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 20%;
  transform: translateX(-50%);
  border-width: 10px 10px 0;
  border-style: solid;
  border-color: white transparent transparent;
}

@keyframes popAndBounce {
  0% {
    transform: scale(0) translateY(80px);
    opacity: 0;
  }
  70% {
    transform: scale(1.2) translateY(80px);
  }
  100% {
    transform: scale(1) translateY(80px);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-20%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-20%) translateY(0);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(80px);
  }
  50% {
    transform: translateY(70px);
  }
}

.hay-bg {
  background-color: var(--hay-yellow);
  background-image: repeating-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0px,
    rgba(255, 255, 255, 0.1) 2px,
    transparent 2px,
    transparent 6px
  );
}

.text-brown-900 {
  color: #3e2723;
}
</style>