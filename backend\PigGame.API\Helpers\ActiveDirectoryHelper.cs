﻿using System.DirectoryServices.AccountManagement;
using System.Runtime.Versioning;

namespace PigGame.API.Helpers
{
    internal static class ActiveDirectoryHelper
    {
        [SupportedOSPlatform("windows")]
        internal static UserDetails GetUserDetailsFromAD(string username)
        {
            // Parse the username (DOMAIN\username)
            var parts = username.Split('\\');
            if (parts.Length != 2)
            {
                throw new ArgumentException("Invalid username format.");
            }

            var domain = parts[0];
            var userName = parts[1];

            // Use PrincipalContext to query Active Directory
            using (var context = new PrincipalContext(ContextType.Domain, domain))
            {
                using (var userPrincipal = UserPrincipal.FindByIdentity(context, userName))
                {
                    if (userPrincipal == null)
                    {
                        throw new Exception("User not found in Active Directory.");
                    }

                    // Return additional user details
                    return new UserDetails
                    {
                        Username = username,
                        FullName = userPrincipal.DisplayName,
                        Email = userPrincipal.EmailAddress,
                        Groups = userPrincipal.GetGroups().Select(g => g.Name).ToList()
                    };
                }
            }
        }
    }

    public class UserDetails
    {
        public required string Username { get; set; } // DOMAIN\username
        public string? FullName { get; set; }
        public string? Email { get; set; }
        public List<string>? Groups { get; set; }
    }
}
