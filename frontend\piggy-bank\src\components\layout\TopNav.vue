<script setup lang="ts">
import { RouterLink } from 'vue-router';
import { useAuthStore } from '@/stores/authStore';

const authStore = useAuthStore();
</script>

<template>
  <nav class="bg-gray-100 p-4 shadow">
    <div class="container mx-auto flex items-center gap-8">
      <!-- Logo/Home -->
      <RouterLink to="/" class="flex items-center gap-2">
        <div class="rounded-full bg-pink-200 p-2">🐷</div>
        <span class="text-lg font-bold">Craving for Saving</span>
      </RouterLink>

      <!-- Navigation Links -->
      <div class="flex gap-6">
        <RouterLink
          :to="{ name: 'DashboardPage' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Dashboard
        </RouterLink>
        <RouterLink
          :to="{ name: 'PigDesigner' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Design Pig
        </RouterLink>
        <RouterLink
          :to="{ name: 'Feeding<PERSON>rea' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Feed Pig
        </RouterLink>
        <RouterLink
          :to="{ name: 'TasksAndGoals' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Tasks & Goals
        </RouterLink>
        <RouterLink
          :to="{ name: 'LeaderboardPage' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Leaderboard
        </RouterLink>
        <RouterLink
          :to="{ name: 'RewardsPage' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Rewards
        </RouterLink>
        <RouterLink
          v-if="authStore.canAccessAdmin()"
          :to="{ name: 'AdminDashboard' }"
          class="transition-colors hover:text-blue-600"
          active-class="text-blue-600"
        >
          Admin
        </RouterLink>
      </div>
    </div>
  </nav>
</template>
