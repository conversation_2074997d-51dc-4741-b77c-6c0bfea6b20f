<script setup lang="ts">
import { ref } from 'vue';
import { checkLoanAppId } from '@/services/taskService';

interface Props {
  isOpen: boolean;
  taskTitle: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['close', 'submit']);
const loanAppId = ref('');
const error = ref('');

const validateAndSubmit = async () => {
  error.value = '';
  if (!loanAppId.value.trim()) {
    error.value = 'Loan App ID is required';
    return;
  }
  if (!/^\d+$/.test(loanAppId.value)) {
    error.value = 'Loan App ID must contain only numbers';
    return;
  }
  
  try {
    const exists = await checkLoanAppId(parseInt(loanAppId.value));
    if (!exists) {
      emit('submit', loanAppId.value);
      loanAppId.value = '';
    } else {
      error.value = 'This Loan App ID has already been claimed';
    }
  } catch (err: unknown) {
    error.value = err instanceof Error ? err.message : 'Failed to validate loan application ID';
  }
};
</script>

<template>
  <div
    v-if="props.isOpen"
    class="fixed inset-0 bg-transparent flex items-center justify-center z-50"
  >
    <div class="bg-white rounded-lg p-6 w-96 shadow-xl">
      <h2 class="text-xl font-bold mb-4">{{ props.taskTitle }}</h2>
      <form @submit.prevent="validateAndSubmit">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Enter Loan App ID
          </label>
          <input
            type="text"
            v-model="loanAppId"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter numbers only"
          />
          <p v-if="error" class="mt-2 text-sm text-red-600">{{ error }}</p>
        </div>
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            @click="emit('close')"
            class="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Submit
          </button>
        </div>
      </form>
    </div>
  </div>
</template>