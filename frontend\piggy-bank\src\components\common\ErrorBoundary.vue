<script setup lang="ts">
import { ref, onErrorCaptured } from 'vue';
import logger from '@/services/loggingService';
import BasePanel from '@/components/common/BasePanel.vue';

const props = defineProps<{
  componentName: string;
}>();

const error = ref<Error | null>(null);
const errorInfo = ref<string | null>(null);

// Capture errors from child components
onErrorCaptured((err, instance, info) => {
  error.value = err as Error;
  errorInfo.value = info;

  // Log the error
  logger.error(
    `Error in component: ${err.message}`,
    props.componentName,
    {
      componentInfo: info,
      componentInstance: instance
        ? {
            name: instance.$options?.name,
            props: instance.$props,
          }
        : 'Unknown',
    },
    err
  );

  // Prevent the error from propagating further
  return false;
});

// Reset the error state
function resetError() {
  error.value = null;
  errorInfo.value = null;
}
</script>

<template>
  <div>
    <!-- Show error UI if there's an error -->
    <BasePanel v-if="error" class="border-red-200 bg-red-50">
      <div class="p-4">
        <div class="mb-4 flex items-center">
          <div class="mr-3 rounded-full bg-red-100 p-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-red-800">Something went wrong</h3>
        </div>

        <div class="mb-4">
          <p class="text-sm text-red-700">{{ error.message }}</p>
          <p v-if="errorInfo" class="mt-1 text-xs text-red-600">
            {{ errorInfo }}
          </p>
        </div>

        <div class="flex justify-end">
          <button
            @click="resetError"
            class="rounded bg-red-100 px-4 py-2 text-red-800 transition-colors hover:bg-red-200"
          >
            Try Again
          </button>
        </div>
      </div>
    </BasePanel>

    <!-- Render default slot if no error -->
    <slot v-else></slot>
  </div>
</template>
