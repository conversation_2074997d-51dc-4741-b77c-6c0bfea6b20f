CREATE PROCEDURE [dbo].[spGetLogs]
    @Level NVARCHAR(10) = NULL,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @UserId INT = NULL,
    @SearchText NVARCHAR(255) = NULL,
    @PageNumber INT = 1,
    @PageSize INT = 50,
    @SessionId NVARCHAR(100) = null
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Calculate pagination values
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Build dynamic query for filtering
    SELECT 
        l.Id,
        l.Timestamp,
        l.Level,
        l.Message,
        l.Source,
        l.UserId,
        u.UserName AS UserName,
        l.SessionId,
        l.<PERSON>,
        l.<PERSON>ackTrace
    FROM 
        Logs l
        LEFT JOIN Users u ON l.UserId = u.Id
    WHERE 
        (@Level IS NULL OR Level = @Level)
        AND (@StartDate IS NULL OR Timestamp >= @StartDate)
        AND (@EndDate IS NULL OR Timestamp <= @EndDate)
        AND (@UserId IS NULL OR UserId = @UserId)
        AND(@SessionId IS NULL OR l.SessionId = @SessionId)
        AND (@SearchText IS NULL 
            OR l.Message LIKE '%' + @SearchText + '%' 
            OR l.Source LIKE '%' + @SearchText + '%'
            OR u.UserName LIKE '%' + @SearchText + '%'
            OR l.ContextJson LIKE '%' + @SearchText + '%'
            OR l.SessionId LIKE '%' + @SearchText + '%')
        
    ORDER BY 
        Timestamp DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY;
END
