CREATE PROCEDURE [dbo].[spGetLogsCount]
    @Level NVARCHAR(10) = NULL,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @UserId INT = NULL,
    @SearchText NVARCHAR(255) = NULL,
    @SessionId NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Count total records matching the filter
    SELECT 
        COUNT(*)
    FROM 
        Logs l
        LEFT JOIN Users u ON l.UserId = u.Id
    WHERE 
        (@Level IS NULL OR Level = @Level)
        AND (@StartDate IS NULL OR Timestamp >= @StartDate)
        AND (@EndDate IS NULL OR Timestamp <= @EndDate)
        AND (@UserId IS NULL OR UserId = @UserId)
        AND (@SessionId IS NULL OR l.SessionID = @sessionId)
        AND (@SearchText IS NULL 
            OR l.Message LIKE '%' + @SearchText + '%' 
            OR l.Source LIKE '%' + @SearchText + '%'
            OR l.UserName LIKE '%' + @SearchText + '%'
            OR l.ContextJson LIKE '%' + @SearchText + '%'
            OR l.SessionId LIKE '%' + @SearchText + '%');
END
