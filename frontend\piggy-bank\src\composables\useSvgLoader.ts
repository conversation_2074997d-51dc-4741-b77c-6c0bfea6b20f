// src/composables/useSvgLoader.ts
import { ref, watch } from 'vue';

export function useSvgLoader(path: string) {
  const svgContent = ref('');
  const isLoading = ref(false);
  const error = ref<Error | null>(null);

  async function loadSvg(svgPath: string) {
    if (!svgPath) {
      svgContent.value = '';
      return;
    }

    isLoading.value = true;
    error.value = null;

    try {
      const response = await fetch(svgPath);
      if (!response.ok) {
        throw new Error(`Failed to load SVG: ${response.statusText}`);
      }

      const text = await response.text();

      // Extract the entire SVG content without the outer svg tag
      const svgMatch = text.match(/<svg[^>]*>([\s\S]*?)<\/svg>/i);
      const innerContent = svgMatch ? svgMatch[1] : text;
      svgContent.value = innerContent;
    } catch (err) {
      error.value = err instanceof Error ? err : new Error(String(err));
      console.error('Error loading SVG:', err);
    } finally {
      isLoading.value = false;
    }
  }

  // Load SVG when path changes
  watch(() => path, loadSvg, { immediate: true });

  return {
    svgContent,
    isLoading,
    error,
  };
}
