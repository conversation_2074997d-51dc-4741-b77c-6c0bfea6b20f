using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PigGameLibrary.Data;
using PigGameLibrary.Models;
using System.Text;
using System.Text.Json;

namespace PigGame.API.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class LogsController : ControllerBase
    {
        private readonly ILogData _db;
        private readonly ILogger<LogsController> _logger;
        private readonly IUserData _userDb;

        public LogsController(ILogData db, ILogger<LogsController> logger, IUserData userDb)
        {
            _db = db;
            _logger = logger;
            _userDb = userDb;
        }

        // GET: api/Logs
        [HttpGet]
        [Authorize]
        public async Task<ActionResult<object>> GetLogs(
            [FromQuery] string? level = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? userId = null,
            [FromQuery] string? searchText = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 50,
            [FromQuery] string? userName = null,
            [FromQuery] string? sessionId = null)
        {
            // Get the current user's username from the identity
            if (User.Identity?.Name == null)
            {
                return Unauthorized("User is not authenticated.");
            }

            string cleanUserName = User.Identity.Name.Contains('\\') 
                ? User.Identity.Name.Split('\\')[1] 
                : User.Identity.Name;

            // Get the user from the database to check their role
            var user = await _userDb.GetUserByName(cleanUserName);
            
            // Check if user is SuperAdmin
            if (user == null || user.Role != "SuperAdmin")
            {
                return Forbid();
            }

            try
            {
                var logs = await _db.GetLogs(level, startDate, endDate, userId, searchText, page, pageSize,userName, sessionId);
                var totalCount = await _db.GetLogsCount(level, startDate, endDate, userId, searchText);

                return Ok(new { logs, totalCount });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving logs");
                return StatusCode(500, "An error occurred while retrieving logs");
            }
        }

        // POST: api/Logs
        [HttpPost]
        public async Task<IActionResult> CreateLog([FromBody] LogEntryModel logEntry)
        {
            try
            {
                // Set timestamp to UTC now if not provided
                if (logEntry.Timestamp == default)
                {
                    logEntry.Timestamp = DateTime.UtcNow;
                }

                var id = await _db.CreateLogEntry(logEntry);
                
                if (id <= 0)
                {
                    return BadRequest("Failed to create log entry");
                }

                return Ok(new { Id = id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating log entry");
                return StatusCode(500, "An error occurred while creating the log entry");
            }
        }

        // GET: api/Logs/export
        [HttpGet("export")]
        [Authorize]
        public async Task<IActionResult> ExportLogs(
            [FromQuery] string? level = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int? userId = null,
            [FromQuery] string? searchText = null,
            [FromQuery] string format = "json")
        {
            // Get the current user's username from the identity
            if (User.Identity?.Name == null)
            {
                return Unauthorized("User is not authenticated.");
            }

            string cleanUserName = User.Identity.Name.Contains('\\') 
                ? User.Identity.Name.Split('\\')[1] 
                : User.Identity.Name;

            // Get the user from the database to check their role
            var user = await _userDb.GetUserByName(cleanUserName);
            
            // Check if user is SuperAdmin
            if (user == null || user.Role != "SuperAdmin")
            {
                return Forbid();
            }

            try
            {
                // Get all logs matching the filter (no pagination for export)
                var logs = await _db.GetLogs(level, startDate, endDate, userId, searchText, 1, 10000);

                if (format.ToLower() == "csv")
                {
                    // Generate CSV
                    var csv = new StringBuilder();
                    
                    // Add header
                    csv.AppendLine("Id,Timestamp,Level,Message,Source,UserId,UserName,SessionId");
                    
                    // Add data rows
                    foreach (var log in logs)
                    {
                        csv.AppendLine($"{log.Id},{log.Timestamp:yyyy-MM-dd HH:mm:ss},{log.Level},\"{EscapeCsvField(log.Message)}\",{log.Source},{log.UserId ?? 0},{log.UserName ?? ""},\"{log.SessionId ?? ""}\"");
                    }
                    
                    // Return CSV file
                    return File(Encoding.UTF8.GetBytes(csv.ToString()), "text/csv", "logs.csv");
                }
                else
                {
                    // Return JSON file
                    var json = JsonSerializer.Serialize(logs, new JsonSerializerOptions { WriteIndented = true });
                    return File(Encoding.UTF8.GetBytes(json), "application/json", "logs.json");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting logs");
                return StatusCode(500, "An error occurred while exporting logs");
            }
        }

        // DELETE: api/Logs/before/{date}
        [HttpDelete("before/{date}")]
        [Authorize]
        public async Task<IActionResult> DeleteLogsBefore(DateTime date)
        {
            // Get the current user's username from the identity
            if (User.Identity?.Name == null)
            {
                return Unauthorized("User is not authenticated.");
            }

            string cleanUserName = User.Identity.Name.Contains('\\') 
                ? User.Identity.Name.Split('\\')[1] 
                : User.Identity.Name;

            // Get the user from the database to check their role
            var user = await _userDb.GetUserByName(cleanUserName);
            
            // Check if user is SuperAdmin
            if (user == null || user.Role != "SuperAdmin")
            {
                return Forbid();
            }

            try
            {
                var rowsDeleted = await _db.DeleteLogsBefore(date);
                return Ok(new { RowsDeleted = rowsDeleted });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting logs");
                return StatusCode(500, "An error occurred while deleting logs");
            }
        }

        // Helper method to escape CSV fields
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return string.Empty;
                
            // Replace double quotes with two double quotes
            return field.Replace("\"", "\"\"");
        }
    }
}





