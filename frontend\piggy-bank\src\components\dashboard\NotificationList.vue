<!-- src/components/dashboard/NotificationList.vue -->
<script setup lang="ts">
import { onMounted } from 'vue';
import BasePanel from '@/components/common/BasePanel.vue';
import { useActivityStore } from '@/stores/activityStore';
import type { Activity } from '@/types/activity';

const activityStore = useActivityStore();

onMounted(async () => {
  await activityStore.loadActivities();
});

const getActivityIcon = (type: Activity['type']) => {
  switch (type) {
    case 'level_up':
      return '🎉';
    case 'coins':
      return '💰';
    case 'achievement':
      return '🏆';
    case 'task':
      return '✅';
    default:
      return '📝';
  }
};

const formatTimestamp = (date: Date) => {
  const now = new Date();
  const diffMinutes = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60)
  );

  if (diffMinutes < 60) {
    return `${diffMinutes} minutes ago`;
  } else if (diffMinutes < 24 * 60) {
    const hours = Math.floor(diffMinutes / 60);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  } else {
    return date.toLocaleDateString();
  }
};
</script>

<template>
  <BasePanel>
    <h2 class="mb-4 text-xl font-semibold">Recent Activity</h2>
    <div v-if="activityStore.isLoading" class="flex justify-center py-4">
      <span class="animate-spin text-2xl">🔄</span>
    </div>
    <div
      v-else-if="activityStore.activities.length === 0"
      class="py-4 text-center text-gray-500"
    >
      No recent activity to display
    </div>
    <div v-else class="space-y-3">
      <div
        v-for="activity in activityStore.activities"
        :key="activity.id"
        class="activity-item hover:bg-opacity-75 rounded-lg p-3 transition-colors"
        :class="{
          'bg-green-50': activity.type === 'coins',
          'bg-purple-50': activity.type === 'level_up',
          'bg-yellow-50': activity.type === 'achievement',
          'bg-blue-50': activity.type === 'task',
        }"
      >
        <div class="flex items-start gap-3">
          <span class="text-2xl">{{ getActivityIcon(activity.type) }}</span>
          <div class="flex-1">
            <p class="font-medium">{{ activity.message }}</p>
            <div class="mt-1 flex items-center gap-2">
              <span class="text-sm text-gray-500">
                {{ formatTimestamp(new Date(activity.timestamp)) }}
              </span>
              <span
                v-if="activity.metadata?.coins"
                class="rounded-full bg-yellow-100 px-2 py-0.5 text-xs font-medium text-yellow-800"
              >
                +{{ activity.metadata.coins }} coins
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasePanel>
</template>

<style scoped>
/* Remove the @apply style since we're using the utility classes directly in the template */
</style>
