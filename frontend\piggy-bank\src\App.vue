<!-- src/App.vue -->
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useUserStore } from './stores/userStore';
import { usePigStore } from './stores/pigStore';
import { RouterView } from 'vue-router';
import TopNav from './components/layout/TopNav.vue';
import LoadingSpinner from './components/common/LoadingSpinner.vue';
import logger from './services/loggingService';
import ErrorBoundary from './components/common/ErrorBoundary.vue';

const userStore = useUserStore();
const pigStore = usePigStore();
const isAppReady = ref(false);
const authError = ref<string | null>(null);

onMounted(async () => {
  logger.info('Application starting', 'App.onMounted');

  try {
    logger.debug('Loading initial application data', 'App.onMounted');

    await userStore.loadUserInfo();
    await pigStore.loadPig();

    isAppReady.value = true;
    logger.info('Application initialized successfully', 'App.onMounted');
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    authError.value = errorMessage;

    logger.error(
      `Application initialization failed: ${errorMessage}`,
      'App.onMounted',
      { error: errorMessage },
      error instanceof Error ? error : new Error(errorMessage)
    );
  }
});
</script>

<template>
  <ErrorBoundary componentName="App">
    <div>
      <!-- Loading state -->
      <div
        v-if="!isAppReady && !authError"
        class="bg-opacity-75 fixed inset-0 flex items-center justify-center bg-white"
      >
        <div class="flex flex-row items-center">
          <LoadingSpinner />
          <p class="ml-3 text-lg text-gray-700">Loading user information...</p>
        </div>
      </div>

      <!-- Authentication error state -->
      <div
        v-else-if="authError"
        class="fixed inset-0 flex items-center justify-center"
      >
        <div class="max-w-md rounded-lg bg-white p-6 shadow-lg">
          <h2 class="mb-4 text-xl text-red-600">Authentication Error</h2>
          <p class="mb-4">{{ authError }}</p>
          <p>
            You may need to refresh the page or contact support if the problem
            persists.
          </p>
        </div>
      </div>

      <!-- Main app content -->
      <div v-else>
        <TopNav />
        <RouterView />
      </div>
    </div>
  </ErrorBoundary>
</template>
