<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>d8cea3a9-de3e-4031-bf3d-eeea5d103523</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Negotiate" Version="9.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.2" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="8.0.0" />
    <PackageReference Include="System.DirectoryServices.AccountManagement" Version="9.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\PigGameLibrary\PigGameLibrary.csproj" />
  </ItemGroup>

</Project>
