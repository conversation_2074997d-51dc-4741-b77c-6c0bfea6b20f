import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'https://localhost:7057';

const httpClient = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for Windows Auth
  headers: {
    'Content-Type': 'application/json',
  },
});

// Simple request interceptor
httpClient.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    console.error('API request error:', error);
    return Promise.reject(error);
  }
);

// Simple response interceptor for global error handling
httpClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API error:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.warn('Authentication error');
    }
    return Promise.reject(error);
  }
);

export default httpClient;
