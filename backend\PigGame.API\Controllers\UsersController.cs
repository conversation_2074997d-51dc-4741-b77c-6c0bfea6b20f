﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PigGameLibrary.Data;
using PigGameLibrary.Models;
using PigGame.API.Helpers;
using PigGame.API.Models.Requests;
using Microsoft.Data.SqlClient;

namespace PigGame.API.Controllers
{
    [Route("[controller]")]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IUserData _db;
        public UsersController(IUserData db)
        {
            _db = db;
        }

        [HttpGet("info")]
        [Authorize]
        public async Task<IActionResult> GetUserInfo()
        {
            if (!OperatingSystem.IsWindows())
            {
                return StatusCode(StatusCodes.Status501NotImplemented, "This endpoint is only supported on Windows platforms.");
            }

            var user = HttpContext.User;
            if (user.Identity?.IsAuthenticated != true || string.IsNullOrEmpty(user.Identity.Name))
            {
                return Unauthorized("User is not authenticated.");
            }

            string cleanUserName = user.Identity.Name.Contains('\\') ? user.Identity.Name.Split('\\')[1] : user.Identity.Name;

            // check if user exists in our db
            var existingUser = await _db.GetUserByName(cleanUserName);

            if (existingUser == null)
            {
                UserDetails userDetails;
                try
                {
                    userDetails = ActiveDirectoryHelper.GetUserDetailsFromAD(user.Identity.Name);
                }
                catch (Exception e)
                {
                    return StatusCode(StatusCodes.Status500InternalServerError, "Could not retrieve user details from AD.");
                }

                // create user in our db if we see they're new
                existingUser = await _db.CreateUser(cleanUserName, userDetails.FullName ?? string.Empty, userDetails.Email ?? string.Empty);
            }
            else
            {
                // Update the last login date
                existingUser = await _db.UpdateUserLastLogin(cleanUserName);
            }

            return Ok(new
            {
                Username = cleanUserName,
                existingUser.DisplayName,
                Email = existingUser.Email,
                Id = existingUser.Id,
                Coins = existingUser.Coins,
                UnclaimedCoins = existingUser.UnclaimedCoins,
                LastLoginDate = existingUser.LastLoginDate,
                Role = existingUser.Role
            });
        }

        [HttpGet]
        public async Task<ActionResult<List<UserModel>>> Get()
        {
            var output = await _db.GetAllUsers();
            return Ok(output);
        }

        // GET api/<UsersController>/5
        [HttpGet("{id}")]
        public async Task<ActionResult<UserModel>> GetUserById(int id)
        {
            var output = await _db.GetUserById(id);
            if (output == null)
            {
                return NotFound($"Id not found for user {id}");
            }
            return Ok(output);
        }

        //// POST api/<UsersController>
        [HttpPost]
        public async Task<ActionResult<UserModel>> Create([FromBody] CreateUserRequest request)
        {
            var createdUser = await _db.CreateUser(request.UserName, request.DisplayName, request.Email, request.Role);

            if (createdUser == null)
                return BadRequest("User could not be created.");

            return CreatedAtAction(nameof(Get), new { id = createdUser.Id }, createdUser);
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            var rowAffected = await _db.DeleteUserById(id);
            if(rowAffected == 0)
            {
                return NotFound($"No User found with id {id}");
            }
            return NoContent();
        }

        [HttpPatch("{userId}/coins")]
        public async Task<IActionResult> EditUserCoins(int userId, [FromBody] int coins)
        {
            try
            {
                int rowsAffected = await _db.EditUserCoins(userId, coins);

                return rowsAffected > 0 ? NoContent() : BadRequest("No rows were updated");
            }
            catch (SqlException ex)
            {
                if (ex.Message.Contains("User does not exist")) return NotFound("User not found.");

                if (ex.Message.Contains("Not enough coins")) return BadRequest("Not enough coins to subtract.");

                return StatusCode(500, "An unexperted database error occured.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Unexpected error: {ex.Message}");
            }
            

            
        }

        [HttpGet("leaderboard")]
        public async Task<ActionResult<List<object>>> GetLeaderboard()
        {
            var leaderboard = await _db.GetLeaderboardData();
            return Ok(leaderboard);
        }

        // This should probably live on the pig along with coins, but for now it's here
        [HttpPost("{userid}/feed")]
        public async Task<IActionResult> FeedPig(int userId, [FromBody] int coins)
        {
            if (coins <= 0)
            {
                return BadRequest("Coins to feed must be greater than zero.");
            }

            // Get current user to check available unclaimed coins
            var user = await _db.GetUserById(userId);
            if (user == null)
            {
                return NotFound("User not found.");
            }

            if (user.UnclaimedCoins < coins)
            {
                return BadRequest("Not enough unclaimed coins available.");
            }

            // Transfer coins from unclaimed to regular coins
            await _db.FeedPigCoins(userId, coins);

            return NoContent();

        }

        [HttpPatch("{userId}/role")]
        [Authorize]
        public async Task<IActionResult> UpdateUserRole(int userId, [FromBody] string role)
        {
            // Get the current user to check if they have permission to update roles
            var currentUser = HttpContext.User;
            if (currentUser.Identity?.IsAuthenticated != true || string.IsNullOrEmpty(currentUser.Identity.Name))
            {
                return Unauthorized("User is not authenticated.");
            }

            string cleanUserName = currentUser.Identity.Name.Contains('\\') ? currentUser.Identity.Name.Split('\\')[1] : currentUser.Identity.Name;
            var adminUser = await _db.GetUserByName(cleanUserName);

            // Only SuperAdmin can change roles
            if (adminUser == null || adminUser.Role != "SuperAdmin")
            {
                return Forbid("Only SuperAdmin users can change roles.");
            }

            // Validate the role
            if (role != "User" && role != "Admin" && role != "SuperAdmin" && role != "Guest")
            {
                return BadRequest("Invalid role. Role must be 'User', 'Admin', 'SuperAdmin', or 'Guest'.");
            }

            var rowsAffected = await _db.UpdateUserRole(userId, role);

            if (rowsAffected == 0)
            {
                return NotFound($"No user found with userId {userId}");
            }

            return NoContent();
        }
    }
}
