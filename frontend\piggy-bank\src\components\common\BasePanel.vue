<script setup lang="ts">
const props = defineProps<{
  title?: string;
  maxHeight?: string;
}>();
</script>

<template>
  <div
    class="overflow-hidden rounded-xl border border-pink-100 bg-gradient-to-br from-pink-200 to-white shadow-lg"
  >
    <!-- Panel Header -->
    <div
      v-if="props.title"
      class="bg-gradient-to-r from-pink-500 to-rose-400 px-6 py-4"
    >
      <h2 class="text-xl font-bold text-white">{{ props.title }}</h2>
    </div>

    <!-- Panel Content -->
    <div class="custom-scrollbar max-h-[70vh] space-y-5 overflow-y-auto p-5">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(236, 72, 153, 0.5) rgba(255, 255, 255, 0.1);
}
</style>
