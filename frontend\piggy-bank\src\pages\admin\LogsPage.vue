<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '@/stores/authStore';
import BasePanel from '@/components/common/BasePanel.vue';
import LoadingSpinner from '@/components/common/LoadingSpinner.vue';
import logger, { LogLevel, type LogEntry } from '@/services/loggingService';

const authStore = useAuthStore();
const isLoading = ref(false);
const error = ref<string | null>(null);
const logs = ref<LogEntry[]>([]);
const totalCount = ref(0);
const expandedLogId = ref<number | null>(null);

// Filter state
const filter = ref({
  level: '',
  startDate: '',
  endDate: '',
  userId: null,
  searchText: '',
  page: 1,
  pageSize: 20,
});

// Available log levels for dropdown
const logLevels = Object.values(LogLevel);

// Load logs based on current filter
async function loadLogs() {
  if (!authStore.isSuperAdmin) return;

  isLoading.value = true;
  error.value = null;

  try {
    // Convert dates to proper format if provided
    const result = await logger.getLogs({
      ...filter.value,
      level: filter.value.level ? (filter.value.level as LogLevel) : undefined,
      startDate: filter.value.startDate
        ? new Date(filter.value.startDate)
        : undefined,
      endDate: filter.value.endDate
        ? new Date(filter.value.endDate)
        : undefined,
      userId: filter.value.userId || undefined,
    });
    logs.value = result.logs;
    totalCount.value = result.totalCount;
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load logs';
    console.error(error.value);
  } finally {
    isLoading.value = false;
  }
}

// Handle pagination
function changePage(newPage: number) {
  filter.value.page = newPage;
  loadLogs();
}

// Toggle expanded log details
function toggleLogDetails(logId: number) {
  expandedLogId.value = expandedLogId.value === logId ? null : logId;
}

// Export logs
async function exportLogs(format: 'csv' | 'json') {
  isLoading.value = true;

  try {
    // Convert dates to proper format if provided
    const blob = await logger.exportLogs(
      {
        ...filter.value,
        level: filter.value.level
          ? (filter.value.level as LogLevel)
          : undefined,
        startDate: filter.value.startDate
          ? new Date(filter.value.startDate)
          : undefined,
        endDate: filter.value.endDate
          ? new Date(filter.value.endDate)
          : undefined,
        userId: filter.value.userId || undefined,
      },
      format
    );

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `logs.${format}`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (err) {
    error.value =
      err instanceof Error ? err.message : `Failed to export logs as ${format}`;
    console.error(error.value);
  } finally {
    isLoading.value = false;
  }
}

// Reset filters
function resetFilters() {
  filter.value = {
    level: '',
    startDate: '',
    endDate: '',
    userId: null,
    searchText: '',
    page: 1,
    pageSize: 20,
  };
  loadLogs();
}

// Calculate total pages for pagination
const totalPages = computed(() =>
  Math.ceil(totalCount.value / filter.value.pageSize)
);

// Generate page numbers for pagination
const pageNumbers = computed(() => {
  const current = filter.value.page;
  const total = totalPages.value;
  const pages = [];

  // Always show first page, last page, current page, and 1 page before and after current
  if (total <= 7) {
    // Show all pages if 7 or fewer
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // Always add first page
    pages.push(1);

    // Add ellipsis if needed
    if (current > 3) {
      pages.push('...');
    }

    // Add pages around current page
    const start = Math.max(2, current - 1);
    const end = Math.min(total - 1, current + 1);

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    // Add ellipsis if needed
    if (current < total - 2) {
      pages.push('...');
    }

    // Always add last page if more than 1 page
    if (total > 1) {
      pages.push(total);
    }
  }

  return pages;
});

// Format date for display
function formatDate(date: string | Date) {
  // Ensure input is treated as UTC
  const utcDate = typeof date === 'string'
    ? new Date(date + 'Z') // Force UTC
    : new Date(date);

  return utcDate.toLocaleString('en-US', {
    timeZone: 'America/Chicago',  // Central Time (auto switches to CDT/CST)
    year: 'numeric',
    month: 'numeric',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  });
}


// Load logs on component mount
onMounted(() => {
  if (authStore.isSuperAdmin) {
    loadLogs();
  }
});
</script>

<template>
  <div>
    <h2 class="mb-4 text-2xl font-bold">System Logs</h2>

    <div
      v-if="!authStore.isSuperAdmin"
      class="rounded-lg bg-red-100 p-4 text-red-700"
    >
      You don't have permission to view logs.
    </div>

    <div v-else>
      <!-- Filters -->
      <BasePanel class="mb-4">
        <h3 class="mb-3 text-lg font-semibold">Filters</h3>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <!-- Log Level -->
          <div>
            <label class="mb-1 block text-sm font-medium">Log Level</label>
            <select
              v-model="filter.level"
              class="w-full rounded-lg border border-gray-300 px-3 py-2"
            >
              <option value="">All Levels</option>
              <option v-for="level in logLevels" :key="level" :value="level">
                {{ level }}
              </option>
            </select>
          </div>

          <!-- Start Date -->
          <div>
            <label class="mb-1 block text-sm font-medium">Start Date</label>
            <input
              v-model="filter.startDate"
              type="date"
              class="w-full rounded-lg border border-gray-300 px-3 py-2"
            />
          </div>

          <!-- End Date -->
          <div>
            <label class="mb-1 block text-sm font-medium">End Date</label>
            <input
              v-model="filter.endDate"
              type="date"
              class="w-full rounded-lg border border-gray-300 px-3 py-2"
            />
          </div>

          <!-- User ID -->
          <div>
            <label class="mb-1 block text-sm font-medium">User ID</label>
            <input
              v-model.number="filter.userId"
              type="number"
              class="w-full rounded-lg border border-gray-300 px-3 py-2"
              placeholder="Filter by user ID"
            />
          </div>

          <!-- Search Text -->
          <div class="md:col-span-2">
            <label class="mb-1 block text-sm font-medium">Search Text</label>
            <input
              v-model="filter.searchText"
              type="text"
              class="w-full rounded-lg border border-gray-300 px-3 py-2"
              placeholder="Search in message, source, username..."
            />
          </div>

          <!-- Action Buttons -->
          <div class="flex items-end space-x-2 md:col-span-2">
            <button
              @click="loadLogs"
              class="rounded-lg bg-blue-500 px-4 py-2 font-semibold text-white hover:bg-blue-600"
            >
              Apply Filters
            </button>
            <button
              @click="resetFilters"
              class="rounded-lg border border-gray-300 bg-white px-4 py-2 font-semibold text-gray-700 hover:bg-gray-100"
            >
              Reset
            </button>
            <div class="ml-auto flex space-x-2">
              <button
                @click="exportLogs('csv')"
                class="rounded-lg bg-green-500 px-4 py-2 font-semibold text-white hover:bg-green-600"
                :disabled="isLoading"
              >
                Export CSV
              </button>
              <button
                @click="exportLogs('json')"
                class="rounded-lg bg-purple-500 px-4 py-2 font-semibold text-white hover:bg-purple-600"
                :disabled="isLoading"
              >
                Export JSON
              </button>
            </div>
          </div>
        </div>
      </BasePanel>

      <!-- Results -->
      <BasePanel>
        <div v-if="isLoading && !logs.length" class="flex justify-center py-8">
          <LoadingSpinner />
        </div>

        <div v-else-if="error" class="rounded-lg bg-red-100 p-4 text-red-700">
          {{ error }}
        </div>

        <div v-else-if="!logs.length" class="py-8 text-center text-gray-500">
          No logs found matching your criteria.
        </div>

        <div v-else>
          <!-- Log count summary -->
          <div class="mb-4 text-sm text-gray-600">
            Showing {{ logs.length }} of {{ totalCount }} logs
          </div>

          <!-- Logs table -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    ID
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    Timestamp
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    Level
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    Message
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    Source
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    User
                  </th>
                  <th
                    class="px-4 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                  >
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="divide-y divide-gray-200 bg-white">
                <template v-for="log in logs" :key="log.id">
                  <tr class="hover:bg-gray-50">
                    <td class="px-4 py-2 whitespace-nowrap">{{ log.id }}</td>
                    <td class="px-4 py-2 whitespace-nowrap">
                      {{ formatDate(log.timestamp) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap">
                      <span
                        :class="{
                          'bg-red-100 text-red-800': log.level === 'ERROR',
                          'bg-yellow-100 text-yellow-800': log.level === 'WARN',
                          'bg-blue-100 text-blue-800': log.level === 'INFO',
                          'bg-gray-100 text-gray-800':
                            log.level === 'DEBUG' || log.level === 'TRACE',
                        }"
                        class="rounded-full px-2 py-1 text-xs font-semibold"
                      >
                        {{ log.level }}
                      </span>
                    </td>
                    <td class="max-w-md truncate px-4 py-2">
                      {{ log.message }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap">
                      {{ log.source }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap">
                      {{ log.userName || log.userId || 'Anonymous' }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap">
                      <button
                        @click="
                          log.id !== undefined && toggleLogDetails(log.id)
                        "
                        class="rounded-lg bg-gray-100 px-2 py-1 text-xs font-medium text-gray-700 hover:bg-gray-200"
                      >
                        {{ expandedLogId === log.id ? 'Hide' : 'Details' }}
                      </button>
                    </td>
                  </tr>
                  <!-- Expanded details row -->
                  <tr v-if="expandedLogId === log.id" class="bg-gray-50">
                    <td colspan="7" class="px-4 py-2">
                      <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                          <h4 class="mb-1 font-semibold">Session ID:</h4>
                          <p class="text-sm">{{ log.sessionId || 'N/A' }}</p>
                        </div>
                        <div>
                          <h4 class="mb-1 font-semibold">User Info:</h4>
                          <p class="text-sm">
                            ID: {{ log.userId || 'N/A' }}, Name:
                            {{ log.userName || 'N/A' }}
                          </p>
                        </div>
                        <div class="md:col-span-2">
                          <h4 class="mb-1 font-semibold">Full Message:</h4>
                          <p class="text-sm whitespace-pre-wrap">
                            {{ log.message }}
                          </p>
                        </div>
                        <div v-if="log.context" class="md:col-span-2">
                          <h4 class="mb-1 font-semibold">Context:</h4>
                          <pre
                            class="max-h-40 overflow-auto rounded bg-gray-100 p-2 text-xs"
                            >{{ JSON.stringify(log.context, null, 2) }}</pre
                          >
                        </div>
                        <div v-if="log.stackTrace" class="md:col-span-2">
                          <h4 class="mb-1 font-semibold">Stack Trace:</h4>
                          <pre
                            class="max-h-40 overflow-auto rounded bg-gray-100 p-2 text-xs"
                            >{{ log.stackTrace }}</pre
                          >
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div
            v-if="totalPages > 1"
            class="mt-4 flex items-center justify-center"
          >
            <nav class="flex items-center space-x-1">
              <button
                @click="changePage(filter.page - 1)"
                :disabled="filter.page === 1"
                :class="[
                  'rounded-lg px-3 py-1 text-sm font-medium',
                  filter.page === 1
                    ? 'cursor-not-allowed text-gray-400'
                    : 'text-gray-700 hover:bg-gray-100',
                ]"
              >
                Previous
              </button>

              <button
                v-for="page in pageNumbers"
                :key="page"
                @click="page !== '...' && changePage(Number(page))"
                :disabled="page === '...'"
                :class="[
                  'rounded-lg px-3 py-1 text-sm font-medium',
                  page === filter.page
                    ? 'bg-blue-500 text-white'
                    : page === '...'
                      ? 'cursor-default text-gray-400'
                      : 'text-gray-700 hover:bg-gray-100',
                ]"
              >
                {{ page }}
              </button>

              <button
                @click="changePage(filter.page + 1)"
                :disabled="filter.page === totalPages"
                :class="[
                  'rounded-lg px-3 py-1 text-sm font-medium',
                  filter.page === totalPages
                    ? 'cursor-not-allowed text-gray-400'
                    : 'text-gray-700 hover:bg-gray-100',
                ]"
              >
                Next
              </button>
            </nav>
          </div>
        </div>
      </BasePanel>
    </div>
  </div>
</template>
