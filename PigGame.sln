﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35825.156
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PigGame.API", "backend\PigGame.API\PigGame.API.csproj", "{4E696558-438A-41FA-A6FD-6DDD4EAA9062}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "backend", "backend", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "frontend", "frontend", "{81E48165-8864-42E8-BA87-16C9778637DC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "database", "database", "{90125B84-E185-48A4-9D35-4633DADB1B25}"
EndProject
Project("{00D1A9C2-B5F0-4AF3-8072-F6C62B433612}") = "PigGame.Database", "PigGame.Database\PigGame.Database.sqlproj", "{725549BE-A9AC-4497-A260-6452A0AE4300}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		README.md = README.md
		PigGameLibrary\Models\UserTaskModel.cs = PigGameLibrary\Models\UserTaskModel.cs
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PigGameLibrary", "PigGameLibrary\PigGameLibrary.csproj", "{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{4E696558-438A-41FA-A6FD-6DDD4EAA9062}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E696558-438A-41FA-A6FD-6DDD4EAA9062}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E696558-438A-41FA-A6FD-6DDD4EAA9062}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E696558-438A-41FA-A6FD-6DDD4EAA9062}.Release|Any CPU.Build.0 = Release|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Release|Any CPU.Build.0 = Release|Any CPU
		{725549BE-A9AC-4497-A260-6452A0AE4300}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{4E696558-438A-41FA-A6FD-6DDD4EAA9062} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{81E48165-8864-42E8-BA87-16C9778637DC} = {8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}
		{725549BE-A9AC-4497-A260-6452A0AE4300} = {90125B84-E185-48A4-9D35-4633DADB1B25}
		{30145E0D-5EC3-44ED-9ED1-CC55B08F56C8} = {90125B84-E185-48A4-9D35-4633DADB1B25}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C2D241A8-D77D-4866-8454-92F5B49081DE}
	EndGlobalSection
EndGlobal
