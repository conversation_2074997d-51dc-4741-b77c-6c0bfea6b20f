import { defineStore } from 'pinia';
import { ref } from 'vue';
import * as activityService from '@/services/activityService';
import type { Activity } from '@/types/activity';

export const useActivityStore = defineStore('activity', () => {
  const activities = ref<Activity[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  async function loadActivities() {
    isLoading.value = true;
    error.value = null;

    try {
      activities.value = await activityService.getUserActivities();
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to load activities';
      console.error('Error loading activities:', err);
    } finally {
      isLoading.value = false;
    }
  }

  async function addActivity(
    type: Activity['type'],
    message: string,
    metadata?: Activity['metadata']
  ) {
    try {
      const newActivity = await activityService.createActivity({
        type,
        message,
        metadata,
      });

      // Add to local state
      activities.value = [newActivity, ...activities.value];
      return newActivity;
    } catch (err) {
      error.value =
        err instanceof Error ? err.message : 'Failed to create activity';
      console.error('Error creating activity:', err);
      return null;
    }
  }

  return {
    activities,
    isLoading,
    error,
    loadActivities,
    addActivity,
  };
});
