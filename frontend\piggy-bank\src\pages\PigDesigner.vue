<!-- src/pages/PigDesigner.vue -->
<script setup lang="ts">
import { computed } from 'vue';
import UserPig from '@/components/pig-designer/UserPig.vue';
import { usePigStore } from '../stores/pigStore';
import PageHeader from '@/components/common/PageHeader.vue';
import ControlPanel from '@/components/pig-designer/ControlPanel.vue';

const pigStore = usePigStore();

const pigName = computed({
  get: () => pigStore.selectedPig.name || '',
  set: (value) => {
    pigStore.selectedPig.name = value;
  },
});

const selectedFacePropId = computed({
  get: () => pigStore.selectedPig.facePropId || '',
  set: (value: string) => {
    pigStore.selectFaceProp(value);
  },
});

const selectedEyesId = computed({
  get: () => pigStore.selectedPig.eyeId || '',
  set: (value: string) => {
    pigStore.selectEye(value);
  },
});

const selectedEyebrowsId = computed({
  get: () => pigStore.selectedPig.eyebrowId || '',
  set: (value: string) => {
    pigStore.selectEyebrow(value);
  },
});

const selectedMouthId = computed({
  get: () => pigStore.selectedPig.mouthId || '',
  set: (value: string) => {
    pigStore.selectMouth(value);
  },
});

function savePigDesign() {
  try {
    if (!pigName.value.trim()) {
      alert('Please give your pig a name!');
      return;
    }
    pigStore.savePig(pigName.value.trim());
    alert(`${pigName.value} has been saved!`);
  } catch (error) {
    alert('Failed to save pig design. Please try again.');
    console.error(error);
  }
}
</script>

<template>
  <div class="p-6">
    <div class="max-w-8xl mx-auto">
      <PageHeader
        title="Pig Designer"
        subTitle="Create your custom pig companion!"
      />

      <div class="flex flex-wrap gap-8">
        <!-- Control Panel -->
        <div class="lg:w-auto">
          <ControlPanel
            :pig-name="pigName"
            @update:pig-name="pigName = $event"
            :eyes="pigStore.eyes"
            :selected-eyes-id="selectedEyesId"
            @update:selected-eyes-id="selectedEyesId = $event"
            :eyebrows="pigStore.eyebrows"
            :selected-eyebrows-id="selectedEyebrowsId"
            @update:selected-eyebrows-id="selectedEyebrowsId = $event"
            :face-props="pigStore.faceProps"
            :selected-face-prop-id="selectedFacePropId"
            @update:selected-face-prop-id="selectedFacePropId = $event"
            :mouths="pigStore.mouths"
            :selected-mouth-id="selectedMouthId"
            @update:selected-mouth-id="selectedMouthId = $event"
            @save="savePigDesign"
          />
        </div>

        <!-- Preview Area -->
        <div
          class="bg-barn-background flex min-h-[500px] flex-1 flex-col items-center justify-center rounded-xl border border-pink-100 bg-cover bg-center p-6 shadow-lg"
        >
          <div class="pig-container relative">
            <div class="pig-shadow"></div>
            <UserPig size="large" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-barn-background {
  background-image: url('/assets/backgrounds/4-farm-life-bg.png');
  background-size: 150%;
  background-position: center bottom;
}

.pig-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.pig-shadow {
  height: 50px;
  width: 300px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  filter: blur(3px);
  position: absolute;
  bottom: -10px;
  left: 20%;
  z-index: 5;
}
</style>
