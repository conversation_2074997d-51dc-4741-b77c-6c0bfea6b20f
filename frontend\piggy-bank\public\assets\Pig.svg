<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 28.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.1" id="레이어_1" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="864px" height="864px"
	 viewBox="0 0 864 864" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FCD4C5;}
	.st1{fill:#F1ACAF;}
	.st2{fill:#D87588;}
	.st3{fill:#F6CBBB;}
	.st4{fill:#E5B8A7;}
	.st5{fill:#BF6F72;}
</style>

<switch>
	<foreignObject requiredExtensions="&ns_ai;" x="0" y="0" width="1" height="1">
		<i:aipgfRef  xlink:href="#adobe_illustrator_pgf">
		</i:aipgfRef>
	</foreignObject>
	<g i:extraneous="self">
		<!-- Body 2 -->
		<path  class="st3" d="M857.651,405.515c-13.903-30.008-37.897-27.301-37.897-27.301c-21.541-17.491-54.543-6.146-64.397-2.143
			c-39.508-57.861-112.419-71.882-112.419-71.882c-69.166-16.965-152.027,3.08-184.078,12.35
			c9.651,25.739,26.753,84.251,7.479,136.922l0.001,0c-0.026,0.081-0.058,0.157-0.084,0.237l-0.033-0.036
			c0,0-27.081,91.159-161.164,117.383c-33.954,6.641-63.109,6.831-87.735,3.424c33.152,24.448,95.776,72.369,112.507,97.384
			l26.809,56.562l0.124,0.268l76.73-0.001l0.087-0.588l10.788-73.66c0,0,40.115,8.973,76.007,11.084l0.02,0.051
			c17.324,0.856,62.046,1.278,103.961-13.246l26.554,76.358l76.727,0.001c10.998-74.415,39.242-176.792,39.242-176.792
			c22.875-68.624,9.679-126.229,9.679-126.229c-2.649-12.292-6.672-23.392-11.684-33.418c5.527-2.307,16.731-6.244,25.953-4.779
			c0,0-25.788,26.85-0.897,47.382c0,0,15.542,11.111,33.152-2.822c0.83-0.657,3.195-2.907,4.193-4.279
			c3.056-4.204,8.974-14.107,6.607-25.164c0,0,21.079,11.056,8.073,41.741c0,0-5,9.427,0,11.507
			c7.157,2.978,12.557-6.092,12.557-6.092C867.517,429.657,857.651,405.515,857.651,405.515z M809.938,418.787
			c-10.312,6.041-12.192-4.486-12.192-4.486c-1.171-8.175,6.746-13.677,10.779-15.917c0,0,3.382-1.8,5.206,1.287
			C813.731,399.671,821.127,412.479,809.938,418.787z"/>
		<!-- Head group -->
		<g transform="translate(76, 161) scale(0.49)">
			<path class="st0" d="M860,16.487c-1.198-18.413-26.499-17.03-37.395-15.569c-3.01,0.404-12.046,1.679-12.046,1.679l-96.367,11.704
				c-81.507,13.679-93.205,74.353-94.69,97.224h0C558.243,72.421,498.49,62.03,462.993,59.683
				c-10.659-0.776-21.712-0.992-31.842-0.93c-5.823,0.036-11.347,0.162-16.302,0.332c-5.982,0.076-13.304,0.386-21.711,1.135
				c-0.738,0.055-1.133,0.087-1.133,0.087l-0.017,0.017c-36.165,3.359-91.894,15.024-148.88,51.526
				c-1.392-22.603-12.794-83.801-94.708-97.549L50.589,2.422c0,0-7.77-1.138-11.019-1.558C28.46-0.574,3.775-1.696,2.591,16.487
				c0,0,0.113,5.099,0.363,13.513c0,0,0,0,0,0c0.939,31.572,3.816,109.987,9.963,142.382c0.756,3.986,2.851,12.641,4.185,17.616
				c5.08,18.947,23.247,67.329,80.506,94.539c-0.277,0.561-0.547,1.096-0.827,1.668c-17.248,35.178-40.032,90.033-54.119,156.9
				c-9.04,60.339-16.545,184.533,51.941,275.614c0.003,0.004,0.544,0.756,1.595,2.115c8.053,10.468,17.13,20.481,27.344,29.894
				c13.561,13.092,32.025,28.889,55.492,44.809c55.054,35.093,136.489,66.242,252.139,66.242
				c264.837,0,350.276-163.345,350.276-163.345l0.05,0.081c0.08-0.143,0.169-0.277,0.249-0.42l-0.002,0
				c56.994-94.232,45.21-213.802,36.377-265.317c-14.442-63.359-36.316-115.115-52.779-148.416c0.003-0.001,0.005-0.003,0.008-0.004
				c0.961-0.46,1.886-0.939,2.825-1.41c52.445-26.32,70.799-70.491,76.621-90.486c1.879-6.453,4.042-15.783,4.766-19.515
				c6.221-32.038,9.127-111.192,10.071-142.95h0C859.887,21.586,860,16.487,860,16.487z"/>
			<!-- Default eyes -->
			<!-- <g>
				<ellipse cx="237.251" cy="450.037" rx="37.491" ry="49.72"/>
				<ellipse cx="624.516" cy="450.037" rx="37.491" ry="49.72"/>
			</g> -->
			<g>
				<path class="st1" d="M162.675,78.242C122.613,49.899,37.564,35.06,2.954,30v0c0,0,0,0,0,0
					c0.939,31.572,3.816,109.987,9.963,142.382c0.756,3.986,2.851,12.641,4.185,17.616c5.08,18.947,23.247,67.329,80.506,94.539
					c0,0,0,0,0,0c12.986-26.283,22.633-41.148,22.633-41.148c25.615-44.008,54.713-77.535,84.601-103.114
					C188.927,92.374,162.675,78.242,162.675,78.242z"/>
				<path class="st1" d="M859.637,30c-34.609,5.059-119.658,19.898-159.72,48.242c0,0-26.146,14.069-42.072,61.734
					c30.008,25.629,59.235,59.237,84.948,103.414c0,0,9.604,14.782,22.552,40.974l0,0c0.003-0.001,0.005-0.003,0.008-0.004
					c0.961-0.46,1.886-0.939,2.825-1.41c52.445-26.32,70.799-70.491,76.621-90.486c1.879-6.453,4.042-15.783,4.766-19.515
					C855.787,140.912,858.692,61.758,859.637,30L859.637,30z"/>
			</g>
			<path class="st1" d="M541.001,523.432c-1.448-1.73-3.514-4.19-5.38-6.379c-7.353-9.021-43.604-49.329-103.543-48.199
				c-0.076-0.001-0.149,0.001-0.224,0.001c-0.074,0-0.148-0.003-0.224-0.001c-59.939-1.13-96.189,39.178-103.543,48.199
				c-1.866,2.189-3.932,4.649-5.38,6.379c-55.203,65.14-24.909,122.887-3.066,150.562c5.451,6.686,19.218,21.284,38.959,26.377
				c0,0,5.849,1.469,12.135,1.767c14.717,0.699,23.405-3.862,23.405-3.862c13.109-6.558,26.063-8.531,37.49-8.276
				c0.05-0.001,0.102,0,0.154,0v0c0.023,0,0.047,0,0.07,0c0.023,0,0.047,0.001,0.07,0v0c0.052-0.001,0.103-0.002,0.154,0
				c11.427-0.254,24.381,1.719,37.49,8.276c0,0,8.688,4.561,23.405,3.862c6.285-0.299,12.135-1.767,12.135-1.767
				c19.741-5.092,33.508-19.69,38.959-26.377C565.91,646.318,596.204,588.571,541.001,523.432z"/>
			<g>
				<ellipse class="st2" cx="383.533" cy="585.648" rx="13.881" ry="22.695"/>
				<ellipse class="st2" cx="476.99" cy="585.648" rx="13.881" ry="22.695"/>
			</g>
		</g>
		<g>
		  <!-- Shading for back legs -->
			<path class="st4" d="M520.394,665.57l19.694,56.8l69.866,0.004l14.401-70.051C582.44,666.848,537.718,666.426,520.394,665.57z"/>
			<path class="st4" d="M217.323,574.469c-1.161-0.161-2.283-0.35-3.424-0.527c5.055,14.215,24.42,71.783,35.369,148.431h0.169
				h69.898h0.133l10.36-50.521C313.099,646.838,250.476,598.917,217.323,574.469z"/>
		</g>
		<!-- Hooves -->
		<g>
			<path class="st5" d="M319.337,722.374h-69.898c-0.117,0.439-0.187,0.923-0.141,1.517l3.076,31.944c0,0,0.486,8.553,8.419,4.539
				c0,0,9.024-3.999,19.292-12.733l0,0c0,0,0.004-0.004,0.004-0.004c0.279-0.237,0.558-0.478,0.839-0.722
				c0.928-0.751,2.426-1.795,3.508-1.748c0.112,0.003,0.214,0.023,0.32,0.036c0.461,0.082,1.403,0.367,2.7,1.375
				c10.769,9.479,20.527,13.796,20.527,13.796c7.933,4.015,8.419-4.539,8.419-4.539l3.076-31.944
				C319.523,723.297,319.453,722.812,319.337,722.374z"/>
			<path class="st5" d="M609.962,722.374h-69.898c-0.117,0.439-0.187,0.923-0.141,1.517l3.076,31.944c0,0,0.486,8.553,8.419,4.539
				c0,0,9.024-3.999,19.292-12.733l0,0c0,0,0.004-0.004,0.004-0.004c0.279-0.237,0.558-0.478,0.839-0.722
				c0.928-0.751,2.426-1.795,3.508-1.748c0.112,0.003,0.214,0.023,0.32,0.036c0.461,0.082,1.403,0.367,2.7,1.375
				c10.769,9.479,20.527,13.796,20.527,13.796c7.933,4.015,8.419-4.539,8.419-4.539l3.076-31.944
				C610.148,723.297,610.078,722.812,609.962,722.374z"/>
			<path class="st5" d="M433.493,728.683l-3.363,34.926c0,0-0.531,9.352-9.205,4.962c0,0-10.323-4.576-21.867-14.588
				c-2.269-1.979-3.792-2.045-4.16-2.02c-0.03,0.004-0.06,0.006-0.09,0.009l0,0.003c-0.958,0.017-2.827,1.399-3.895,2.253
				c-11.428,9.841-21.582,14.343-21.582,14.343c-8.674,4.39-9.205-4.962-9.205-4.962l-3.363-34.926H433.493z"/>
			<path class="st5" d="M727.636,728.683l-3.363,34.926c0,0-0.531,9.352-9.205,4.962c0,0-10.323-4.576-21.867-14.588
				c-2.269-1.979-3.792-2.045-4.16-2.02c-0.03,0.004-0.06,0.006-0.09,0.009l0,0.003c-0.958,0.017-2.827,1.399-3.895,2.253
				c-11.427,9.841-21.582,14.343-21.582,14.343c-8.674,4.39-9.205-4.962-9.205-4.962l-3.363-34.926H727.636z"/>
		</g>
	</g>
</switch>
</svg>
