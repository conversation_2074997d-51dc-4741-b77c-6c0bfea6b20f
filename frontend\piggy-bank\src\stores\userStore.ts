import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import {
  fetchUserInfo,
  feedPigCoins,
  getLeaderboard,
  type UserInfo,
  type LeaderboardModel,
} from '@/services/userService';
import logger from '@/services/loggingService';

export const useUserStore = defineStore('user', () => {
  // State
  const _balance = ref({
    coins: 0,
    unclaimedCoins: 0,
  });
  const userInfo = ref<UserInfo | null>(null);
  const leaderboardUsers = ref<LeaderboardModel[] | null>(null);
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  // Computed
  const coins = computed(() => _balance.value.coins);
  const unclaimedCoins = computed(() => _balance.value.unclaimedCoins);

  const firstName = computed(() => {
    if (!userInfo.value?.displayName) return '';
    return userInfo.value.displayName.split(' ')[0];
  });

  // leaderboard stuff
  const currentRank = computed(() => {
    if (!leaderboardUsers.value || !userInfo.value?.id) return null;

    // find user's position in the leaderboard
    const position = leaderboardUsers.value.findIndex(
      (user) => user.userId === userInfo.value?.id
    );

    return position !== -1 ? position + 1 : null;
  });

  const nextRankUser = computed(() => {
    if (
      !leaderboardUsers.value ||
      !userInfo.value?.id ||
      currentRank.value === null
    )
      return null;

    // return the user at the next position - need position -2 because 0 based array
    return leaderboardUsers.value[currentRank.value - 2];
  });

  const coinsToNextRank = computed(() => {
    if (!nextRankUser.value || !userInfo.value?.coins) return null;
    return nextRankUser.value.coins - userInfo.value.coins;
  });

  const progressToNextRank = computed(() => {
    if (
      !nextRankUser.value ||
      !userInfo.value?.coins ||
      coinsToNextRank.value === null ||
      coinsToNextRank.value === 0
    ) {
      return 100; // Already at the top or tied
    }

    // Calculate progress percentage
    const currentUserCoins = userInfo.value.coins;
    const nextUserCoins = nextRankUser.value.coins;

    // Handle the case where the user is very close
    if (nextUserCoins - currentUserCoins <= 0) return 100;

    // Start with the last place (0 coins) as the baseline
    const minCoins = 0;
    const totalRange = nextUserCoins - minCoins;
    const userProgress = currentUserCoins - minCoins;

    return Math.min(Math.round((userProgress / totalRange) * 100), 99);
  });

  const isInTopFive = computed(() => {
    return currentRank.value !== null && currentRank.value <= 5;
  });

  const coinsToTopFive = computed(() => {
    if (isInTopFive.value || !leaderboardUsers.value || !userInfo.value) {
      return null;
    }

    // Get the user at rank 5
    const fifthPlaceUser = leaderboardUsers.value[4];
    if (!fifthPlaceUser) return null;

    // Calculate coins needed to reach 5th place
    const coinsNeeded = fifthPlaceUser.coins - (userInfo.value.coins || 0);
    return coinsNeeded > 0 ? coinsNeeded : 0;
  });

  // Actions
  async function loadUserInfo() {
    isLoading.value = true;
    error.value = null;
    logger.debug('Loading user information', 'userStore.loadUserInfo');

    try {
      const info = await fetchUserInfo();
      userInfo.value = info;
      _balance.value.coins = info.coins;
      _balance.value.unclaimedCoins = info.unclaimedCoins;

      logger.info(
        `User ${info.userName} (ID: ${info.id}) logged in successfully`,
        'userStore.loadUserInfo',
        {
          userId: info.id,
          role: info.role,
          coins: info.coins,
          lastLogin: info.lastLoginDate,
        }
      );

      return info;
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load user info';
      error.value = errorMessage;

      logger.error(
        `Failed to load user info: ${errorMessage}`,
        'userStore.loadUserInfo',
        { error: errorMessage },
        err instanceof Error ? err : new Error(errorMessage)
      );

      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function loadLeaderboard() {
    isLoading.value = true;
    error.value = null;
    logger.debug('Loading leaderboard data', 'userStore.loadLeaderboard');

    try {
      leaderboardUsers.value = await getLeaderboard();

      logger.info(
        'Leaderboard loaded successfully',
        'userStore.loadLeaderboard',
        {
          userCount: leaderboardUsers.value.length,
          topUser:
            leaderboardUsers.value.length > 0
              ? leaderboardUsers.value[0].displayName
              : 'none',
        }
      );

      return leaderboardUsers.value;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      error.value = errorMessage;

      logger.error(
        `Failed to load leaderboard: ${errorMessage}`,
        'userStore.loadLeaderboard',
        { error: errorMessage },
        err instanceof Error ? err : new Error(errorMessage)
      );

      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function feedPig(amount: number): Promise<boolean> {
    if (!userInfo.value?.id) {
      const errorMsg = 'User information not loaded.';
      error.value = errorMsg;
      logger.warn(errorMsg, 'userStore.feedPig', { amount });
      return false;
    }

    if (amount > _balance.value.unclaimedCoins) {
      const errorMsg = 'Not enough unclaimed coins.';
      error.value = errorMsg;
      logger.warn(errorMsg, 'userStore.feedPig', {
        userId: userInfo.value.id,
        amount,
        availableCoins: _balance.value.unclaimedCoins,
      });
      return false;
    }

    const userId = userInfo.value.id;

    logger.debug(`Feeding pig ${amount} coins`, 'userStore.feedPig', {
      userId,
      amount,
      currentUnclaimedCoins: _balance.value.unclaimedCoins,
      currentCoins: _balance.value.coins,
    });

    // Optimistic update
    _balance.value.unclaimedCoins -= amount;
    _balance.value.coins += amount;
    userInfo.value.unclaimedCoins -= amount;
    userInfo.value.coins += amount;

    try {
      await feedPigCoins(userId, amount);

      logger.info(
        `User fed pig ${amount} coins successfully`,
        'userStore.feedPig',
        {
          userId,
          amount,
          newUnclaimedCoins: _balance.value.unclaimedCoins,
          newTotalCoins: _balance.value.coins,
        }
      );

      return true;
    } catch (err) {
      // Rollback on failure
      _balance.value.unclaimedCoins += amount;
      _balance.value.coins -= amount;
      userInfo.value.unclaimedCoins += amount;
      userInfo.value.coins -= amount;

      const errorMessage =
        err instanceof Error ? err.message : 'Failed to feed pig';
      error.value = errorMessage;

      logger.error(
        `Failed to feed pig: ${errorMessage}`,
        'userStore.feedPig',
        {
          userId,
          amount,
          error: errorMessage,
        },
        err instanceof Error ? err : new Error(errorMessage)
      );

      return false;
    }
  }

  // Reset store state
  function reset() {
    const previousUserId = userInfo.value?.id;

    logger.debug('Resetting user store state', 'userStore.reset', {
      previousUserId,
    });

    _balance.value.coins = 0;
    userInfo.value = null;
    isLoading.value = false;
    error.value = null;

    logger.info('User store state reset', 'userStore.reset', {
      previousUserId,
    });
  }

  return {
    // State
    userInfo,
    leaderboardUsers,
    isLoading,
    error,

    // Computed
    coins,
    unclaimedCoins,
    firstName,
    currentRank,
    nextRankUser,
    coinsToNextRank,
    progressToNextRank,
    isInTopFive,
    coinsToTopFive,

    // Actions
    loadUserInfo,
    loadLeaderboard,
    feedPig,
    reset,
  };
});
