﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Dapper;

namespace PigGameLibrary.Databases
{
    public class SqlDataAccess : ISqlDataAccess
    {
        private readonly IConfiguration _config;

        public SqlDataAccess(IConfiguration config)
        {
            _config = config;
        }

        public async Task<List<T>> LoadData<T, U>(string sqlStatement,
                                      U parameters,
                                      string connectionStringName,
                                      bool isStoredProcedure = false)
        {
            string connectionString = _config.GetConnectionString(connectionStringName);

            CommandType commandType = CommandType.Text;

            if (isStoredProcedure == true)
            {
                commandType = CommandType.StoredProcedure;
            }

            using IDbConnection connection = new SqlConnection(connectionString);
            var rows = await connection.QueryAsync<T>(sqlStatement, parameters, commandType: commandType);
            return rows.ToList();
        }

        public async Task SaveData<T>(string sqlStatement,
                                      T parameters,
                                      string connectionStringName,
                                      bool isStoredProcedure = false)
        {
            string connectionString = _config.GetConnectionString(connectionStringName);

            CommandType commandType = CommandType.Text;

            if (isStoredProcedure == true)
            {
                commandType = CommandType.StoredProcedure;
            }

            using IDbConnection connection = new SqlConnection(connectionString);
            await connection.ExecuteAsync(sqlStatement, parameters, commandType: commandType);
        }

        public async Task<int> ExecuteWithRowCount<T>(string sqlStatement,
                                              T parameters,
                                              string connectionStringName,
                                              bool isStoredProcedure = false)
        {
            string connectionString = _config.GetConnectionString(connectionStringName);
            CommandType commandType = isStoredProcedure ? CommandType.StoredProcedure : CommandType.Text;

            using IDbConnection connection = new SqlConnection(connectionString);
            try
            {
                return await connection.ExecuteAsync(sqlStatement, parameters, commandType: commandType);
              
            }catch(SqlException ex)
            {
                throw;
            }
            
        }

    }
}
