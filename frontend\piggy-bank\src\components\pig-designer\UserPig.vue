<script setup lang="ts">
import { onMounted, computed, ref } from 'vue';
import { usePigStore } from '@/stores/pigStore';
import PigFeature from './PigFeature.vue';
import type { UserPig } from '@/types/pig';

const props = defineProps({
  pigId: {
    type: Number,
    default: undefined,
  },
  size: {
    type: String,
    default: 'medium',
  },
  animate: {
    type: Boolean,
    default: false,
  },
  viewBoxSize: {type: String, default:''},
});
const pigViewBox = computed(() => {
  if(props.viewBoxSize) return props.viewBoxSize;
  return '0 0 200 200';
});
const pigStore = usePigStore();
const pigData = ref<UserPig | null>(null);
const loading = ref(false);

const pigTransform = computed(() => {
  switch (props.size) {
    case 'xsmall':
      return 'translate(50, 50) scale(0.15)';
    case 'small':
      return 'translate(50, 50) scale(0.15)';
    case 'medium':
      return 'translate(0, 0) scale(0.25)';
    case 'large':
      return 'translate(0, 0) scale(0.25)';
    default:
      return 'translate(0, 0) scale(0.25)';
  }
});

onMounted(async () => {
  loading.value = true;
  try {
    if (!props.pigId) {
      // if user pig isn't already loaded, do so
      if (pigStore.selectedPig) {
        pigData.value = pigStore.selectedPig;
      } else {
        pigData.value = await pigStore.loadPig();
      }
    } else {
      pigData.value = await pigStore.getPigById(props.pigId);
    }
  } catch (error) {
    console.error('Error loading pig data:', error);
  } finally {
    loading.value = false;
  }
});

const getFeatureById = <T extends { id: string }>(
  collection: T[],
  id: string | null
) => {
  return id ? collection.find((item) => item.id === id) : null;
};

// Get paths for each feature
const bodyPath = computed(() => {
  if (!pigData.value) return '';
  const body = getFeatureById(pigStore.bodies, pigData.value.bodyId);
  return body?.path || '';
});

const eyePath = computed(() => {
  if (!pigData.value) return '';
  const eye = getFeatureById(pigStore.eyes, pigData.value.eyeId);
  return eye?.path || '';
});

const eyebrowPath = computed(() => {
  if (!pigData.value) return '';
  const eyebrow = getFeatureById(pigStore.eyebrows, pigData.value.eyebrowId);
  return eyebrow?.path || '';
});

const facePropPath = computed(() => {
  if (!pigData.value) return '';
  const faceProp = getFeatureById(pigStore.faceProps, pigData.value.facePropId);
  return faceProp?.path || '';
});

const mouthPath = computed(() => {
  if (!pigData.value) return '';
  const mouth = getFeatureById(pigStore.mouths, pigData.value.mouthId);
  return mouth?.path || '';
});

</script>

<template>
  <div
    class="relative flex flex-col rounded-lg"
    :class="{
      'h-20': size === 'xsmall',
      'h-40': size === 'small',
      'h-64': size === 'medium',
      'h-96': size === 'large',
    }"
  >
    <div
      class="relative flex h-full w-full items-center justify-center overflow-hidden"
    >
      <svg
        :viewBox="pigViewBox"
        class="h-full w-full"
        preserveAspectRatio="xMidYMid meet"
      >
        <g id="pig-container"
         :transform="pigTransform"
         :class="{ 'griddy-dance' : props.animate}">
          <PigFeature
            :path="bodyPath"
            :key="'body-' + bodyPath"
            class="pig-body"
          />

          <PigFeature
            :path="eyePath"
            :key="'eye-' + eyePath"
            :x="93"
            :y="285"
            :scale="3.2"
            class="pig-eye"
          />

          <PigFeature
            :path="eyebrowPath"
            :key="'eyebrow-' + eyebrowPath"
            :x="93"
            :y="285"
            :scale="3.2"
            class="pig-eyebrow"
          />

          <PigFeature
            :path="facePropPath"
            :key="'faceProp-' + facePropPath"
            :x="63"
            :y="290"
            :scale="3.2"
            class="pig-face-prop"
          />

          <PigFeature
            :path="mouthPath"
            :key="'mouth-' + mouthPath"
            :x="115"
            :y="370"
            :scale="3.2"
            class="pig-mouth"
          />
        </g>
      </svg>
    </div>
  </div>
</template>

<style scoped>
@keyframes griddy-dance {
  0% { transform: translateX(0) rotate(0deg); }
  10% { transform: translateX(-5px) rotate(-5deg); }
  20% { transform: translateX(5px) rotate(5deg); }
  30% { transform: translateY(-5px); }
  40% { transform: translateY(5px); }
  50% { transform: rotate(0deg); }
  60% { transform: translateX(-5px) rotate(5deg); }
  70% { transform: translateX(5px) rotate(-5deg); }
  80% { transform: translateY(-5px); }
  90% { transform: translateY(5px); }
  100% { transform: translateX(0) rotate(0deg); }
}

.griddy-dance {
  animation: griddy-dance 1.5s infinite ease-in-out;
  transform-box: fill-box;
  transform-origin: center;
}
</style>
