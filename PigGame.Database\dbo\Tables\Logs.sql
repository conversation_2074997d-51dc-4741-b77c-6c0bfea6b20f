CREATE TABLE [dbo].[Logs]
(
    [Id] INT NOT NULL PRIMARY KEY IDENTITY,
    [Timestamp] DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    [Level] NVARCHAR(10) NOT NULL,
    [Message] NVARCHAR(MAX) NOT NULL,
    [Source] NVARCHAR(255) NOT NULL,
    [UserId] INT NULL,
    [UserName] NVARCHAR(100) NULL,
    [SessionId] NVARCHAR(100) NULL,
    [ContextJson] NVARCHAR(MAX) NULL,
    [StackTrace] NVARCHAR(MAX) NULL,
    CONSTRAINT [FK_Logs_Users] FOREIGN KEY ([UserId]) REFERENCES [Users]([Id]) ON DELETE SET NULL
)
