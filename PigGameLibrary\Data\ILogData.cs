using PigGameLibrary.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PigGameLibrary.Data
{
    public interface ILogData
    {
        Task<int> CreateLogEntry(LogEntryModel logEntry);
        Task<List<LogEntryModel>> GetLogs(
            string? level = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int? userId = null,
            string? searchText = null,
            int page = 1,
            int pageSize = 50,
            string? userName = null,
            string? sessionId = null);
        Task<int> GetLogsCount(
            string? level = null, 
            DateTime? startDate = null, 
            DateTime? endDate = null, 
            int? userId = null, 
            string? searchText = null);
        Task<List<LogEntryModel>> GetLogsBySessionId(string sessionId, int limit = 100);
        Task<int> DeleteLogsBefore(DateTime date);
    }
}
