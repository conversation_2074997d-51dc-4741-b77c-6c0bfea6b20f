# Combined .gitignore for Vue/Node.js and .NET Monorepo

# Node.js / Frontend
node_modules/
dist/
dist-ssr/
coverage/
*.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# .NET / Visual Studio
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/
.vs/
Generated\ Files/
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.VisualState.xml
TestResult.xml
nunit-*.xml
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates
project.lock.json
project.fragment.lock.json
artifacts/
ScaffoldingReadMe.txt

# Visual Studio code coverage results
*.coverage
*.coveragexml

# NuGet Packages
*.nupkg
*.snupkg
**/[Pp]ackages/*
!**/[Pp]ackages/build/
*.nuget.props
*.nuget.targets

# SQL Server files
*.mdf
*.ldf
*.ndf

# Business Intelligence projects
*.rdl.data
*.bim.layout
*.bim_*.settings
*.rptproj.rsuser

# Editor directories and files
# .vscode/*
# !.vscode/extensions.json
# !.vscode/tasks.json
# !.vscode/launch.json
# !.vscode/settings.json
.idea/
*.swp
*.swo
*.sw?
.DS_Store

# VS Code workspace file
# Uncomment the following line to exclude the workspace file 
# *.code-workspace

# .NET Core
*.dll
*.exe
*.pdb

# Common Visual Studio files to ignore
_ReSharper*/
*.[Rr]e[Ss]harper
*.DotSettings.user
_NCrunch_*
.*crunch*.local.xml
nCrunchTemp_*
*.pidb
*.svclog
*.scc
*.dbmdl
*.dbproj.schemaview
*.jfm
*.pfx
*.publishsettings

# Specific to this project
# Add any project-specific ignore rules here
