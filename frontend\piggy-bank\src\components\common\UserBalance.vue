<script setup lang="ts">
import { useUserStore } from '@/stores/userStore';
import CoinReward from '@/components/common/CoinReward.vue';
import { computed } from 'vue';

const userStore = useUserStore();

// Use computed properties to ensure reactivity
const savedCoins = computed(() => userStore.coins);
const unclaimedCoins = computed(() => userStore.unclaimedCoins);
</script>

<template>
  <div class="coin-balance-container">
    <div class="coin-type">
      <div class="coin-label">Saved Coins:</div>
      <CoinReward :amount="savedCoins" />
    </div>

    <div class="coin-type">
      <div class="coin-label">Unclaimed Coins:</div>
      <CoinReward :amount="unclaimedCoins" />
    </div>
  </div>
</template>

<style scoped>
.coin-balance-container {
  display: flex;
  gap: 1.5rem;
  align-items: center;
}

.coin-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.coin-label {
  font-weight: 600;
  color: #5d4037;
}
</style>
