using Microsoft.Extensions.Configuration;
using PigGameLibrary.Databases;
using PigGameLibrary.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PigGameLibrary.Data
{
    public class RewardTierData : IRewardTierData
    {
        private readonly ISqlDataAccess _db;
        private readonly string connectionStringName = "SqlDb";

        public RewardTierData(ISqlDataAccess db)
        {
            _db = db;
        }

        public async Task<List<RewardTierModel>> GetAllRewardTiers()
        {
            var results = await _db.LoadData<RewardTierModel, dynamic>(
                "dbo.spGetRewardTiers",
                new { },
                connectionStringName,
                true);

            return results;
        }

        public async Task<RewardTierModel?> GetRewardTierByCoins(int coins)
        {
            var results = await _db.LoadData<RewardTierModel, dynamic>(
                "dbo.spGetRewardTierByCoins",
                new { Coins = coins },
                connectionStringName,
                true);

            return results.Count > 0 ? results[0] : null;
        }
    }
}