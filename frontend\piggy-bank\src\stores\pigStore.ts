// src/stores/pigStore.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import partsData from '../data/pig-parts.json';
import type { PigFeature, UserPig } from '@/types/pig';
import { useUserStore } from './userStore';
import { getPigByUserId, updatePig, createPig } from '@/services/pigService';
import axios from 'axios';

const DEFAULT_BODY_ID = 'default';
const DEFAULT_EYE_ID = 'default';

export const usePigStore = defineStore('pigParts', () => {
  const userStore = useUserStore();
  // Initialize with saved design or defaults
  //const savedDesign = loadSavedDesign();

  // All available pig parts
  const bodies = ref<PigFeature[]>(partsData.bodies);
  const faceProps = ref<PigFeature[]>(partsData.faceProps);
  const eyes = ref<PigFeature[]>(partsData.eyes);
  const eyebrows = ref<PigFeature[]>(partsData.eyebrows);
  const mouths = ref<PigFeature[]>(partsData.mouths);

  // Currently selected pig configuration
  const selectedPig = ref<UserPig>({
    bodyId: DEFAULT_BODY_ID,
    facePropId: null,
    eyeId: DEFAULT_EYE_ID,
    eyebrowId: null,
    mouthId: null,
    name: undefined,
  });

  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const pigId = ref<number | null>(null);

  // Load pig from API
  async function loadPig(): Promise<UserPig> {
    if (!userStore.userInfo?.id) {
      error.value = 'User not authenticated';
      throw new Error('User not authenticated');
    }

    isLoading.value = true;
    error.value = null;
    try {
      const pig = await getPigByUserId(userStore.userInfo.id);
      pigId.value = pig.id;

      // update store for the logged in user
      selectedPig.value = {
        name: pig.pigName,
        bodyId: DEFAULT_BODY_ID,
        facePropId: pig.facePropId || null,
        eyeId: pig.eyeId || 'default',
        eyebrowId: pig.eyebrowId || null,
        mouthId: pig.mouthId || null,
      };
      return selectedPig.value;
    } catch (err: unknown) {
      // If pig not found (404), just use default pig
      if (axios.isAxiosError(err) && err.response?.status === 404) {
        console.log('No pig found for user, using default pig');
        return selectedPig.value;
      }

      error.value = err instanceof Error ? err.message : 'Failed to load pig';
      console.error(error);
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function getPigById(id: number): Promise<UserPig> {
    isLoading.value = true;
    error.value = null;
    try {
      const pig = await getPigByUserId(id);
      return {
        name: pig.pigName,
        bodyId: DEFAULT_BODY_ID,
        facePropId: pig.facePropId || null,
        eyeId: pig.eyeId || 'default',
        eyebrowId: pig.eyebrowId || null,
        mouthId: pig.mouthId || null,
      };
    } catch (err: unknown) {
      error.value = err instanceof Error ? err.message : 'Failed to load pig';
      console.error(error);
      throw err;
    } finally {
      isLoading.value = false;
    }
  }

  async function savePig(name: string): Promise<void> {
    if (!userStore.userInfo?.id) {
      error.value = 'User not authenticated';
      return;
    }

    isLoading.value = true;
    error.value = null;
    try {
      const pig = {
        userId: userStore.userInfo.id,
        name: name,
        facePropId: selectedPig.value.facePropId,
        eyeId: selectedPig.value.eyeId,
        eyebrowId: selectedPig.value.eyebrowId,
        mouthId: selectedPig.value.mouthId,
      };

      if (pigId.value) {
        // update existing pig
        await updatePig(pigId.value, pig);
      } else {
        // create new pig
        const newPig = await createPig(pig);
        pigId.value = newPig.id;
      }

      // update local state
      selectedPig.value = {
        ...selectedPig.value,
        name,
      };

      console.log('Saved pig design:', selectedPig.value);
    } catch (err: unknown) {
      error.value = err instanceof Error ? err.message : 'Failed to save pig';
      console.error(error);
    } finally {
      isLoading.value = false;
    }
  }

  // Computed properties for selected parts
  const selectedBody = computed(() =>
    bodies.value.find((part) => part.id === selectedPig.value.bodyId)
  );

  const selectedFaceProp = computed(() =>
    faceProps.value.find((part) => part.id === selectedPig.value.facePropId)
  );

  const selectedEye = computed(() =>
    eyes.value.find((part) => part.id === selectedPig.value.eyeId)
  );

  const selectedEyebrow = computed(() =>
    eyebrows.value.find((part) => part.id === selectedPig.value.eyebrowId)
  );

  const selectedMouth = computed(() =>
    mouths.value.find((part) => part.id === selectedPig.value.mouthId)
  );

  // Functions to update selections
  function selectFaceProp(facePropId: string) {
    selectedPig.value.facePropId = facePropId;
  }

  function selectEye(eyeId: string) {
    selectedPig.value.eyeId = eyeId;
  }

  function selectEyebrow(eyebrowId: string) {
    selectedPig.value.eyebrowId = eyebrowId;
  }

  function selectMouth(mouthId: string) {
    selectedPig.value.mouthId = mouthId;
  }

  return {
    // All parts
    bodies,
    faceProps,
    eyes,
    eyebrows,
    mouths,

    // Selected parts
    selectedPig,
    selectedBody,
    selectedFaceProp,
    selectedEye,
    selectedEyebrow,
    selectedMouth,

    // Actions
    selectFaceProp,
    selectEye,
    selectEyebrow,
    selectMouth,
    loadPig,
    getPigById,
    savePig,
  };
});
